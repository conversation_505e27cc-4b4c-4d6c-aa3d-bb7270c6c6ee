<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title><PERSON>ka<PERSON>i {{ $user->name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h2 {
            margin: 0;
            padding: 0;
            font-size: 18px;
        }
        .header p {
            margin: 5px 0;
            font-size: 14px;
        }
        .user-info {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: right;
        }
        .page-break {
            page-break-after: always;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .badge {
            display: inline-block;
            padding: 3px 6px;
            font-size: 10px;
            font-weight: bold;
            border-radius: 3px;
        }
        .badge-success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .badge-warning {
            background-color: #fcf8e3;
            color: #8a6d3b;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>{{ $namaPerusahaan }}</h2>
        <p>{{ $alamatPerusahaan }}</p>
        <h3>REKAP ABSENSI PEGAWAI</h3>
        <p>Tanggal: {{ date('d F Y') }}</p>
    </div>

    <div class="user-info">
        <table style="border: none; margin-bottom: 0;">
            <tr>
                <td style="border: none; width: 20%;">Nama</td>
                <td style="border: none; width: 5%;">:</td>
                <td style="border: none; width: 75%;">{{ $user->name }}</td>
            </tr>
            <tr>
                <td style="border: none;">Email</td>
                <td style="border: none;">:</td>
                <td style="border: none;">{{ $user->email }}</td>
            </tr>
            <tr>
                <td style="border: none;">NIK</td>
                <td style="border: none;">:</td>
                <td style="border: none;">{{ $user->nik ?? '-' }}</td>
            </tr>
            <tr>
                <td style="border: none;">Jabatan</td>
                <td style="border: none;">:</td>
                <td style="border: none;">{{ $user->jabatan ?? '-' }}</td>
            </tr>
        </table>
    </div>

    <table>
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="15%">Tanggal</th>
                <th width="10%">Status</th>
                <th width="15%">Jam Masuk</th>
                <th width="15%">Jam Keluar</th>
                <th width="40%">Keterangan</th>
            </tr>
        </thead>
        <tbody>
            @forelse($absensis as $index => $absensi)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>{{ \Carbon\Carbon::parse($absensi->tanggal)->format('d/m/Y') }}</td>
                    <td>
                        @php
                            try {
                                $jamMasuk = \App\Models\Setting::getValue('jam_masuk', '08:00:00');
                                $toleransiKeterlambatan = (int)\App\Models\Setting::getValue('toleransi_keterlambatan', '15');

                                // Pastikan format jam masuk konsisten
                                if (strlen($jamMasuk) == 5) {
                                    $jamMasuk .= ':00';
                                }

                                $jamMasukDateTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
                                $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransiKeterlambatan);
                                $isTerlambat = false;

                                if ($absensi->jam_masuk) {
                                    $jamMasukUser = \Carbon\Carbon::parse($absensi->jam_masuk);
                                    $isTerlambat = $jamMasukUser->gt($batasKeterlambatan);
                                }
                            } catch (\Exception $e) {
                                $isTerlambat = false;
                            }
                        @endphp

                        @if($isTerlambat)
                            <span class="badge badge-warning">Terlambat</span>
                        @else
                            <span class="badge badge-success">Tepat Waktu</span>
                        @endif
                    </td>
                    <td>
                        @php
                            try {
                                echo $absensi->jam_masuk ? \Carbon\Carbon::parse($absensi->jam_masuk)->format('H:i:s') : '-';
                            } catch (\Exception $e) {
                                echo '-';
                            }
                        @endphp
                    </td>
                    <td>
                        @php
                            try {
                                echo $absensi->jam_keluar ? \Carbon\Carbon::parse($absensi->jam_keluar)->format('H:i:s') : '-';
                            } catch (\Exception $e) {
                                echo '-';
                            }
                        @endphp
                    </td>
                    <td>{{ $absensi->keterangan ?? '-' }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="6" class="text-center">Tidak ada data absensi</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <div class="footer">
        <p>Dicetak pada: {{ date('d F Y H:i:s') }}</p>
        <p>Oleh: {{ Auth::user()->name }}</p>
    </div>
</body>
</html>
