@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Absens<PERSON> Masuk</h5>
                    <div id="current-time" class="text-white"></div>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <form method="POST" action="{{ route('absen.masuk.submit') }}" id="absenForm" enctype="multipart/form-data">
                        @csrf

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">Informasi Absensi</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="tanggal" class="form-label">Tanggal</label>
                                            <input type="date" name="tanggal" id="tanggal" class="form-control @error('tanggal') is-invalid @enderror" value="{{ date('Y-m-d') }}" readonly>
                                            @error('tanggal')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="jam_masuk" class="form-label">Jam Masuk</label>
                                            <input type="text" id="jam_display" class="form-control" value="{{ date('H:i:s') }}" readonly>
                                            <input type="hidden" name="jam_masuk" id="jam_masuk" value="{{ date('H:i:s') }}">
                                            @error('jam_masuk')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <input type="hidden" name="lokasi_masuk" id="lokasi_masuk" value="Absensi Online">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">Foto & Tanda Tangan</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="foto" class="form-label">Foto Selfie</label>
                                            <div class="d-flex flex-column align-items-center">
                                                <div class="video-container mb-2">
                                                    <video id="video" width="100%" height="200" autoplay playsinline muted></video>
                                                    <div id="camera-status" class="camera-status">Memuat kamera...</div>
                                                </div>
                                                <div class="d-flex gap-2">
                                                    <button type="button" id="captureBtn" class="btn btn-primary" disabled>
                                                        <i class="bi bi-camera"></i> Ambil Foto
                                                    </button>
                                                    <button type="button" id="retakeBtn" class="btn btn-secondary" style="display:none;">
                                                        <i class="bi bi-arrow-repeat"></i> Ambil Ulang
                                                    </button>
                                                    <button type="button" id="switchCameraBtn" class="btn btn-info">
                                                        <i class="bi bi-arrow-repeat"></i> Ganti Kamera
                                                    </button>
                                                </div>
                                                <div id="photo-preview" class="mt-2" style="display:none;">
                                                    <img id="preview-img" src="" alt="Preview" style="max-width: 100%; max-height: 200px; border: 1px solid #ccc; border-radius: 4px;">
                                                </div>
                                                <canvas id="canvas" style="display:none;"></canvas>
                                                <input type="hidden" name="foto_masuk" id="foto_masuk">
                                                <div id="upload-container" class="mt-3" style="display:none;">
                                                    <div class="alert alert-warning">
                                                        <strong>Kamera tidak dapat diakses.</strong> Silakan upload foto selfie Anda.
                                                    </div>
                                                    <input type="file" id="photo-upload" class="form-control" accept="image/*">
                                                    <div class="form-text">Format: JPG, PNG. Maks: 2MB</div>
                                                </div>
                                                @error('foto_masuk')
                                                    <div class="text-danger mt-2">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="signature-pad" class="form-label">Tanda Tangan Digital</label>
                                            <div class="border rounded p-2 bg-white">
                                                <canvas id="signature-pad" class="signature-pad" width="100%" height="200"></canvas>
                                            </div>
                                            <div class="d-flex justify-content-end mt-2">
                                                <button type="button" id="clear-signature" class="btn btn-sm btn-secondary">
                                                    <i class="bi bi-eraser"></i> Hapus
                                                </button>
                                            </div>
                                            <input type="hidden" name="tanda_tangan" id="tanda_tangan">
                                            @error('tanda_tangan')
                                                <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="keterangan" class="form-label">
                                <i class="bi bi-chat-text me-2"></i>Keterangan
                            </label>
                            <textarea name="keterangan" id="keterangan" class="form-control @error('keterangan') is-invalid @enderror" rows="3" placeholder="Contoh: Terlambat karena macet, ada keperluan mendadak, dll.">{{ old('keterangan') }}</textarea>
                            <small class="form-text text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                Opsional - Tambahkan keterangan jika ada hal khusus yang perlu disampaikan
                            </small>
                            @error('keterangan')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="/dashboard" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Kembali
                            </a>
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                <i class="bi bi-check-circle"></i> Kirim Absensi Masuk
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tampilkan waktu saat ini
    function updateClock() {
        const now = new Date();
        const timeString = now.getHours().toString().padStart(2, '0') + ':' +
                          now.getMinutes().toString().padStart(2, '0') + ':' +
                          now.getSeconds().toString().padStart(2, '0');
        document.getElementById('current-time').textContent = timeString;
        document.getElementById('jam_display').value = timeString;
        document.getElementById('jam_masuk').value = timeString;
    }

    setInterval(updateClock, 1000);
    updateClock();

    // Lokasi tidak diperlukan lagi

    // Webcam capture
    let video = document.getElementById('video');
    let canvas = document.getElementById('canvas');
    let captureBtn = document.getElementById('captureBtn');
    let retakeBtn = document.getElementById('retakeBtn');
    let switchCameraBtn = document.getElementById('switchCameraBtn');
    let fotoInput = document.getElementById('foto_masuk');
    let cameraStatus = document.getElementById('camera-status');
    let photoPreview = document.getElementById('photo-preview');
    let previewImg = document.getElementById('preview-img');
    let uploadContainer = document.getElementById('upload-container');
    let photoUpload = document.getElementById('photo-upload');
    let stream;
    let currentFacingMode = 'user'; // Default: kamera depan

    // Akses kamera dengan opsi yang lebih spesifik dan fallback
    async function startCamera() {
        // Hentikan stream yang ada jika ada
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
        }

        // Update status
        cameraStatus.textContent = 'Memuat kamera...';
        cameraStatus.style.display = 'block';
        captureBtn.disabled = true;

        // Tampilkan video element
        video.style.display = 'block';

        // Cek apakah browser mendukung getUserMedia
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            console.error('Browser tidak mendukung getUserMedia API');
            cameraStatus.textContent = 'Browser tidak mendukung kamera';
            showAlternativePhotoUpload();
            return;
        }

        try {
            // Coba dengan opsi yang lebih spesifik
            const constraints = {
                audio: false,
                video: {
                    facingMode: currentFacingMode,
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                }
            };

            console.log('Mencoba mengakses kamera dengan mode:', currentFacingMode);

            // Minta izin kamera dengan timeout
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Timeout: Akses kamera terlalu lama')), 10000);
            });

            // Race antara getUserMedia dan timeout
            stream = await Promise.race([
                navigator.mediaDevices.getUserMedia(constraints),
                timeoutPromise
            ]);

            // Pasang stream ke video element
            video.srcObject = stream;

            // Tunggu video siap dengan timeout
            const videoReadyPromise = new Promise((resolve, reject) => {
                video.onloadedmetadata = resolve;
                video.onerror = reject;
                // Tambahkan timeout untuk metadata loading
                setTimeout(() => reject(new Error('Timeout: Video metadata loading terlalu lama')), 5000);
            });

            await videoReadyPromise;

            // Coba play video dengan timeout
            try {
                await Promise.race([
                    video.play(),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout: Video play terlalu lama')), 5000))
                ]);

                console.log('Video berhasil diputar');
                // Sembunyikan status setelah video berhasil diputar
                cameraStatus.style.display = 'none';
                // Aktifkan tombol ambil foto
                captureBtn.disabled = false;

                console.log('Video dimensions:', video.videoWidth, 'x', video.videoHeight);

                // Jika tidak ada dimensi video, mungkin ada masalah
                if (!video.videoWidth || !video.videoHeight) {
                    throw new Error('Video dimensions not available');
                }
            } catch (playError) {
                console.error('Error saat memutar video:', playError);
                throw playError; // Re-throw untuk ditangkap oleh catch di luar
            }

            // Handler untuk error video
            video.onerror = function(err) {
                console.error('Video error:', err);
                cameraStatus.textContent = 'Error video: ' + (err.message || 'Unknown error');
                showAlternativePhotoUpload();
            };

            // Tampilkan pesan sukses di konsol
            console.log('Kamera berhasil diakses');
        } catch (error) {
            console.error('Tidak dapat mengakses kamera:', error);

            // Tampilkan pesan error yang lebih informatif
            let errorMessage = 'Tidak dapat mengakses kamera: ' + error.message;

            if (error.name === 'NotAllowedError' || error.message.includes('Permission denied')) {
                errorMessage = 'Izin kamera ditolak. Silakan berikan izin kamera di browser Anda.';
                cameraStatus.textContent = 'Izin kamera ditolak';
                alert('Izin kamera ditolak. Silakan berikan izin kamera di browser Anda, lalu refresh halaman ini.');
            } else if (error.name === 'NotFoundError' || error.message.includes('Requested device not found')) {
                errorMessage = 'Kamera tidak ditemukan. Pastikan perangkat Anda memiliki kamera yang berfungsi.';
                cameraStatus.textContent = 'Kamera tidak ditemukan';
            } else if (error.name === 'NotReadableError' || error.message.includes('Could not start video source')) {
                errorMessage = 'Kamera sedang digunakan oleh aplikasi lain. Tutup aplikasi lain yang mungkin menggunakan kamera.';
                cameraStatus.textContent = 'Kamera sedang digunakan aplikasi lain';
            } else if (error.name === 'OverconstrainedError') {
                errorMessage = 'Kamera tidak mendukung resolusi yang diminta. Mencoba dengan resolusi lebih rendah...';
                cameraStatus.textContent = 'Mencoba resolusi lebih rendah...';

                // Coba lagi dengan resolusi lebih rendah
                try {
                    stream = await navigator.mediaDevices.getUserMedia({
                        audio: false,
                        video: true // Tanpa batasan resolusi
                    });

                    video.srcObject = stream;
                    await video.play();

                    cameraStatus.style.display = 'none';
                    captureBtn.disabled = false;

                    return; // Keluar dari fungsi jika berhasil
                } catch (fallbackError) {
                    console.error('Fallback camera access failed:', fallbackError);
                    errorMessage = 'Tidak dapat mengakses kamera sama sekali. Silakan gunakan opsi upload foto.';
                    cameraStatus.textContent = 'Kamera tidak tersedia';
                }
            } else if (error.message.includes('Timeout')) {
                errorMessage = 'Waktu akses kamera habis. Silakan coba lagi atau gunakan opsi upload foto.';
                cameraStatus.textContent = 'Timeout akses kamera';
            } else {
                cameraStatus.textContent = 'Error: ' + error.message;
            }

            console.log(errorMessage);

            // Tampilkan form alternatif untuk upload foto
            showAlternativePhotoUpload();
        }
    }

    // Fungsi untuk menampilkan form upload foto alternatif
    function showAlternativePhotoUpload() {
        // Sembunyikan video dan tombol
        video.style.display = 'none';
        captureBtn.style.display = 'none';
        switchCameraBtn.style.display = 'none';
        retakeBtn.style.display = 'none';

        // Sembunyikan status kamera jika masih ditampilkan
        cameraStatus.style.display = 'none';

        // Tampilkan form upload dengan pesan yang lebih jelas
        uploadContainer.style.display = 'block';

        // Perbarui pesan di dalam alert
        const alertElement = uploadContainer.querySelector('.alert');
        if (alertElement) {
            alertElement.innerHTML = '<strong>Kamera tidak dapat diakses.</strong> Silakan upload foto selfie Anda menggunakan form di bawah ini.';
        }

        // Tambahkan event listener untuk file upload jika belum ada
        if (!photoUpload.hasAttribute('data-listener')) {
            photoUpload.addEventListener('change', handleFileUpload);
            photoUpload.setAttribute('data-listener', 'true');
        }

        // Fokus ke input file untuk memudahkan user
        setTimeout(() => {
            photoUpload.focus();
        }, 100);

        // Tambahkan instruksi tambahan
        const helpText = document.createElement('div');
        helpText.className = 'alert alert-info mt-2';
        helpText.innerHTML = '<small><i class="bi bi-info-circle"></i> Tip: Gunakan kamera ponsel Anda untuk mengambil foto selfie, lalu upload di sini.</small>';

        // Tambahkan helpText jika belum ada
        if (!uploadContainer.querySelector('.alert-info')) {
            uploadContainer.appendChild(helpText);
        }
    }

    // Fungsi untuk menangani file upload
    function handleFileUpload(e) {
        const file = e.target.files[0];
        if (!file) return;

        // Validasi ukuran file (maks 2MB)
        if (file.size > 2 * 1024 * 1024) {
            alert('Ukuran file terlalu besar. Maksimal 2MB.');
            return;
        }

        // Validasi tipe file
        if (!file.type.match('image.*')) {
            alert('File harus berupa gambar (JPG, PNG).');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(event) {
            // Kompres gambar sebelum disimpan
            const img = new Image();
            img.onload = function() {
                // Buat canvas untuk kompresi
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // Hitung ukuran yang proporsional dengan maksimal lebar/tinggi 640px
                let width = img.width;
                let height = img.height;
                const maxSize = 640;

                if (width > height && width > maxSize) {
                    height = (height / width) * maxSize;
                    width = maxSize;
                } else if (height > maxSize) {
                    width = (width / height) * maxSize;
                    height = maxSize;
                }

                canvas.width = width;
                canvas.height = height;

                // Gambar ke canvas dengan ukuran baru
                ctx.drawImage(img, 0, 0, width, height);

                // Konversi ke base64 dengan kualitas 0.8 (80%)
                const compressedImage = canvas.toDataURL('image/jpeg', 0.8);

                // Simpan ke input hidden
                fotoInput.value = compressedImage;

                // Tampilkan preview
                const previewContainer = document.createElement('div');
                previewContainer.id = 'preview-container';
                previewContainer.innerHTML = `
                    <div class="mt-3 mb-3">
                        <img src="${compressedImage}" style="max-width: 100%; max-height: 200px; border: 1px solid #ccc; border-radius: 4px;">
                    </div>
                `;

                // Hapus preview lama jika ada
                const oldPreview = document.getElementById('preview-container');
                if (oldPreview) {
                    oldPreview.remove();
                }

                // Tambahkan preview baru
                document.getElementById('photo-upload').parentNode.appendChild(previewContainer);

                console.log('Foto berhasil dikompresi, ukuran data:', compressedImage.length);
            };

            img.src = event.target.result;
        };

        reader.readAsDataURL(file);
    }

    // Mulai kamera saat halaman dimuat
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        startCamera();
    } else {
        console.error('Browser tidak mendukung getUserMedia');
        alert('Browser Anda tidak mendukung akses kamera. Silakan gunakan browser modern seperti Chrome, Firefox, atau Edge terbaru.');
    }

    // Ambil foto
    captureBtn.addEventListener('click', function() {
        try {
            // Pastikan video sudah siap
            if (!video.videoWidth) {
                alert('Kamera belum siap. Mohon tunggu sebentar.');
                return;
            }

            // Sesuaikan ukuran canvas dengan video
            canvas.width = video.videoWidth || 640;
            canvas.height = video.videoHeight || 480;

            // Gambar video ke canvas
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

            // Kompres gambar
            let imgData;
            try {
                // Coba dengan kualitas lebih rendah jika ukuran terlalu besar
                imgData = canvas.toDataURL('image/jpeg', 0.7);

                // Jika ukuran masih terlalu besar, kompres lebih lanjut
                if (imgData.length > 500000) { // 500KB
                    console.log('Gambar terlalu besar, mengompres lebih lanjut...');

                    // Buat canvas baru dengan ukuran lebih kecil
                    const tempCanvas = document.createElement('canvas');
                    const tempCtx = tempCanvas.getContext('2d');

                    // Hitung ukuran yang lebih kecil (50% dari ukuran asli)
                    const scaleFactor = 0.5;
                    tempCanvas.width = canvas.width * scaleFactor;
                    tempCanvas.height = canvas.height * scaleFactor;

                    // Gambar ke canvas yang lebih kecil
                    tempCtx.drawImage(canvas, 0, 0, tempCanvas.width, tempCanvas.height);

                    // Konversi ke base64 dengan kualitas lebih rendah
                    imgData = tempCanvas.toDataURL('image/jpeg', 0.6);
                }
            } catch (e) {
                console.error('Error saat mengompres gambar:', e);
                // Fallback ke PNG jika JPEG gagal
                imgData = canvas.toDataURL('image/png');
            }

            // Tampilkan gambar yang diambil
            fotoInput.value = imgData;

            // Tampilkan preview dan sembunyikan video
            video.style.display = 'none';
            cameraStatus.style.display = 'none';
            captureBtn.style.display = 'none';
            switchCameraBtn.style.display = 'none';
            retakeBtn.style.display = 'inline-block';

            // Tampilkan preview gambar
            photoPreview.style.display = 'block';
            previewImg.src = imgData;

            // Tambahkan info ukuran
            const sizeInfo = document.createElement('div');
            sizeInfo.className = 'text-muted small mt-1';
            sizeInfo.textContent = `Ukuran foto: ${Math.round(imgData.length/1024)} KB`;

            // Hapus info ukuran lama jika ada
            const oldSizeInfo = photoPreview.querySelector('.text-muted');
            if (oldSizeInfo) {
                oldSizeInfo.remove();
            }

            // Tambahkan info ukuran baru
            photoPreview.appendChild(sizeInfo);

            // Log untuk debugging
            console.log('Foto berhasil diambil, panjang data:', imgData.length);

            // Hentikan stream kamera untuk menghemat sumber daya
            if (stream) {
                stream.getTracks().forEach(track => {
                    if (track.readyState === 'live') {
                        track.stop();
                    }
                });
            }
        } catch (error) {
            console.error('Error saat mengambil foto:', error);
            alert('Terjadi kesalahan saat mengambil foto: ' + error.message);

            // Tampilkan form alternatif untuk upload foto
            showAlternativePhotoUpload();
        }
    });

    // Ambil ulang foto
    retakeBtn.addEventListener('click', function() {
        // Tampilkan video dan sembunyikan preview
        video.style.display = 'block';
        photoPreview.style.display = 'none';
        captureBtn.style.display = 'inline-block';
        switchCameraBtn.style.display = 'inline-block';
        retakeBtn.style.display = 'none';

        // Hapus nilai foto
        fotoInput.value = '';
        previewImg.src = '';

        // Mulai kamera lagi
        startCamera();
    });

    // Ganti kamera (depan/belakang)
    switchCameraBtn.addEventListener('click', function() {
        // Toggle facing mode
        currentFacingMode = currentFacingMode === 'user' ? 'environment' : 'user';
        console.log('Switching camera to:', currentFacingMode);

        // Restart kamera dengan mode baru
        startCamera();
    });

    // Tanda tangan digital
    const signaturePad = new SignaturePad(document.getElementById('signature-pad'), {
        backgroundColor: 'rgba(255, 255, 255, 0)',
        penColor: 'black',
        minWidth: 1,
        maxWidth: 3
    });

    document.getElementById('clear-signature').addEventListener('click', function() {
        signaturePad.clear();
    });

    // Form submit
    document.getElementById('absenForm').addEventListener('submit', function(e) {
        if (signaturePad.isEmpty()) {
            e.preventDefault();
            alert('Silakan isi tanda tangan Anda');
            return false;
        }

        if (!fotoInput.value) {
            e.preventDefault();
            alert('Silakan ambil foto selfie Anda');
            return false;
        }

        // Lokasi tidak perlu divalidasi lagi

        // Simpan tanda tangan sebagai base64
        document.getElementById('tanda_tangan').value = signaturePad.toDataURL();
    });
});
</script>

<style>
.signature-pad {
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    height: 200px;
    background-color: white;
}

.video-container {
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 4px;
    overflow: hidden;
    background-color: #f0f0f0;
    position: relative;
}

.camera-status {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #666;
    font-style: italic;
    display: block;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 5px 10px;
    border-radius: 4px;
    z-index: 10;
}

#video {
    display: block;
    background-color: #000;
    width: 100%;
    height: 200px;
    object-fit: cover;
}

#canvas {
    width: 100%;
    height: 200px;
    border: 1px solid #ccc;
    border-radius: 4px;
    object-fit: contain;
    background-color: #f0f0f0;
}

#current-time {
    font-size: 1.2rem;
    font-weight: bold;
}
</style>
@endsection
