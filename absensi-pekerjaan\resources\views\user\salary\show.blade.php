@extends('layouts.app')

@section('title', 'Detail Slip Gaji')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-receipt me-2"></i>
                            Slip Gaji - {{ $salary->periode }}
                        </h5>
                        <div>
                            <a href="{{ route('salary.index') }}" class="btn btn-light btn-sm me-2">
                                <i class="bi bi-arrow-left me-1"></i>
                                Kembali
                            </a>
                            @if($salary->status != 'draft')
                                <a href="{{ route('salary.print', $salary->id) }}" class="btn btn-success btn-sm">
                                    <i class="bi bi-printer me-1"></i>
                                    Cetak PDF
                                </a>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Header Info -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <h4 class="fw-bold">{{ auth()->user()->name }}</h4>
                            <p class="text-muted mb-1">{{ auth()->user()->jabatan }}</p>
                            <p class="text-muted mb-1">NIK: {{ auth()->user()->nik }}</p>
                            <p class="text-muted">Periode: {{ $salary->periode }}</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge {{ $salary->status_badge }} fs-6">{{ $salary->status_text }}</span>
                            @if($salary->tanggal_dibuat)
                                <p class="text-muted mt-2 mb-0">
                                    <small>Dibuat: {{ $salary->tanggal_dibuat->format('d/m/Y') }}</small>
                                </p>
                            @endif
                        </div>
                    </div>

                    <!-- Gaji Bersih Card -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">Gaji Bersih</h3>
                                    <h1 class="fw-bold">Rp {{ number_format($salary->gaji_bersih, 0, ',', '.') }}</h1>
                                    <small>Setelah potongan</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detail Pendapatan dan Potongan -->
                    <div class="row">
                        <!-- Pendapatan -->
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-plus-circle me-2"></i>
                                        Pendapatan
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td>Gaji Pokok</td>
                                            <td class="text-end">Rp {{ number_format($salary->gaji_pokok, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Tunjangan Transport</td>
                                            <td class="text-end">Rp {{ number_format($salary->tunjangan_transport, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Tunjangan Makan</td>
                                            <td class="text-end">Rp {{ number_format($salary->tunjangan_makan, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Tunjangan Lainnya</td>
                                            <td class="text-end">Rp {{ number_format($salary->tunjangan_lainnya, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Lembur ({{ $salary->total_jam_lembur }} jam)</td>
                                            <td class="text-end">Rp {{ number_format($salary->total_upah_lembur, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr class="table-success fw-bold">
                                            <td>Total Pendapatan</td>
                                            <td class="text-end">Rp {{ number_format($salary->total_pendapatan, 0, ',', '.') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Potongan -->
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-dash-circle me-2"></i>
                                        Potongan
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td>Keterlambatan</td>
                                            <td class="text-end">Rp {{ number_format($salary->potongan_terlambat, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Alpha</td>
                                            <td class="text-end">Rp {{ number_format($salary->potongan_alpha, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Surat Peringatan</td>
                                            <td class="text-end">Rp {{ number_format($salary->potongan_sp, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Lainnya</td>
                                            <td class="text-end">Rp {{ number_format($salary->potongan_lainnya, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr class="table-danger fw-bold">
                                            <td>Total Potongan</td>
                                            <td class="text-end">Rp {{ number_format($salary->total_potongan, 0, ',', '.') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Kehadiran -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-calendar-check me-2"></i>
                                        Ringkasan Kehadiran
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-2">
                                            <div class="card border-success">
                                                <div class="card-body">
                                                    <h4 class="text-success">{{ $salary->total_hadir }}</h4>
                                                    <small>Hadir</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="card border-warning">
                                                <div class="card-body">
                                                    <h4 class="text-warning">{{ $salary->total_terlambat }}</h4>
                                                    <small>Terlambat</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="card border-danger">
                                                <div class="card-body">
                                                    <h4 class="text-danger">{{ $salary->total_alpha }}</h4>
                                                    <small>Alpha</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="card border-info">
                                                <div class="card-body">
                                                    <h4 class="text-info">{{ $salary->total_izin }}</h4>
                                                    <small>Izin</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="card border-secondary">
                                                <div class="card-body">
                                                    <h4 class="text-secondary">{{ $salary->total_sakit }}</h4>
                                                    <small>Sakit</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="card border-primary">
                                                <div class="card-body">
                                                    <h4 class="text-primary">{{ $salary->total_cuti }}</h4>
                                                    <small>Cuti</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <a href="{{ route('salary.overtime') }}" class="card text-decoration-none border-warning">
                                <div class="card-body text-center">
                                    <i class="bi bi-clock display-6 text-warning"></i>
                                    <h6 class="mt-2">Detail Lembur</h6>
                                    <small class="text-muted">Lihat riwayat lembur bulan ini</small>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ route('salary.deductions') }}" class="card text-decoration-none border-danger">
                                <div class="card-body text-center">
                                    <i class="bi bi-dash-circle display-6 text-danger"></i>
                                    <h6 class="mt-2">Detail Potongan</h6>
                                    <small class="text-muted">Lihat riwayat potongan bulan ini</small>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ route('absen.riwayat') }}" class="card text-decoration-none border-info">
                                <div class="card-body text-center">
                                    <i class="bi bi-calendar-check display-6 text-info"></i>
                                    <h6 class="mt-2">Riwayat Absensi</h6>
                                    <small class="text-muted">Lihat detail kehadiran bulan ini</small>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Footer Info -->
                    @if($salary->status != 'draft')
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">Informasi Slip Gaji</h6>
                                        <div class="row">
                                            @if($salary->tanggal_disetujui)
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>Tanggal Disetujui:</strong> {{ $salary->tanggal_disetujui->format('d/m/Y H:i') }}</p>
                                                </div>
                                            @endif
                                            @if($salary->tanggal_dibayar)
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>Tanggal Dibayar:</strong> {{ $salary->tanggal_dibayar->format('d/m/Y H:i') }}</p>
                                                </div>
                                            @endif
                                        </div>
                                        <small class="text-muted">
                                            Slip gaji ini telah diverifikasi dan disetujui oleh sistem. 
                                            Jika ada pertanyaan, silakan hubungi bagian HRD.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
