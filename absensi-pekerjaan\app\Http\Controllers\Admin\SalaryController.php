<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\MonthlySalary;
use App\Models\OvertimeRecord;
use App\Models\SalaryDeduction;
use App\Models\Absensi;
use App\Models\Setting;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class SalaryController extends Controller
{
    public function index(Request $request)
    {
        $bulan = $request->get('bulan', date('n'));
        $tahun = $request->get('tahun', date('Y'));

        $salaries = MonthlySalary::with('user')
            ->byPeriod($bulan, $tahun)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.salary.index', compact('salaries', 'bulan', 'tahun'));
    }

    public function generate(Request $request)
    {
        $request->validate([
            'bulan' => 'required|integer|min:1|max:12',
            'tahun' => 'required|integer|min:2020|max:2030',
            'user_ids' => 'array'
        ]);

        $bulan = $request->bulan;
        $tahun = $request->tahun;
        $userIds = $request->user_ids ?? User::where('role', 'user')->pluck('id')->toArray();

        $generated = 0;
        $errors = [];

        foreach ($userIds as $userId) {
            try {
                // Cek apakah sudah ada gaji untuk periode ini
                $existing = MonthlySalary::where('user_id', $userId)
                    ->where('bulan', $bulan)
                    ->where('tahun', $tahun)
                    ->first();

                if ($existing) {
                    continue; // Skip jika sudah ada
                }

                $this->generateMonthlySalary($userId, $bulan, $tahun);
                $generated++;
            } catch (\Exception $e) {
                $user = User::find($userId);
                $errors[] = "Error untuk {$user->name}: " . $e->getMessage();
            }
        }

        if ($generated > 0) {
            $message = "Berhasil generate {$generated} slip gaji.";
            if (!empty($errors)) {
                $message .= " Dengan " . count($errors) . " error.";
            }
            return redirect()->back()->with('success', $message);
        } else {
            return redirect()->back()->with('error', 'Tidak ada slip gaji yang di-generate. ' . implode(' ', $errors));
        }
    }

    private function generateMonthlySalary($userId, $bulan, $tahun)
    {
        $user = User::findOrFail($userId);

        // Hitung data kehadiran
        $attendanceData = $this->calculateAttendanceData($userId, $bulan, $tahun);

        // Hitung lembur
        $overtimeData = $this->calculateOvertimeData($userId, $bulan, $tahun);

        // Hitung potongan
        $deductionData = $this->calculateDeductionData($userId, $bulan, $tahun);

        // Buat record gaji bulanan
        $monthlySalary = new MonthlySalary([
            'user_id' => $userId,
            'bulan' => $bulan,
            'tahun' => $tahun,
            'gaji_pokok' => $user->gaji_pokok,
            'tunjangan_transport' => $user->tunjangan_transport,
            'tunjangan_makan' => $user->tunjangan_makan,
            'tunjangan_lainnya' => $user->tunjangan_lainnya,
            'total_jam_lembur' => $overtimeData['total_jam'],
            'upah_lembur_per_jam' => $overtimeData['upah_per_jam'],
            'total_upah_lembur' => $overtimeData['total_upah'],
            'total_hadir' => $attendanceData['hadir'],
            'total_terlambat' => $attendanceData['terlambat'],
            'total_alpha' => $attendanceData['alpha'],
            'total_izin' => $attendanceData['izin'],
            'total_sakit' => $attendanceData['sakit'],
            'total_cuti' => $attendanceData['cuti'],
            'potongan_terlambat' => $deductionData['terlambat'],
            'potongan_alpha' => $deductionData['alpha'],
            'potongan_sp' => $deductionData['sp'],
            'potongan_lainnya' => $deductionData['lainnya'],
            'dibuat_oleh' => auth()->id()
        ]);

        $monthlySalary->calculateTotals();
        $monthlySalary->save();

        return $monthlySalary;
    }

    private function calculateAttendanceData($userId, $bulan, $tahun)
    {
        $absensis = Absensi::where('user_id', $userId)
            ->whereMonth('tanggal', $bulan)
            ->whereYear('tanggal', $tahun)
            ->get();

        $data = [
            'hadir' => 0,
            'terlambat' => 0,
            'alpha' => 0,
            'izin' => 0,
            'sakit' => 0,
            'cuti' => 0
        ];

        // Ambil setting jam masuk dan toleransi
        $jamMasuk = Setting::getValue('jam_masuk', '08:00:00');
        $toleransi = (int)Setting::getValue('toleransi_keterlambatan', '15');

        foreach ($absensis as $absen) {
            switch ($absen->status) {
                case 'hadir':
                    $data['hadir']++;

                    // Cek keterlambatan
                    if ($absen->jam_masuk) {
                        $jamMasukDateTime = Carbon::createFromFormat('H:i:s', $jamMasuk);
                        $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransi);
                        $jamMasukUser = Carbon::parse($absen->jam_masuk);

                        if ($jamMasukUser->gt($batasKeterlambatan)) {
                            $data['terlambat']++;
                        }
                    }
                    break;
                case 'alpha':
                    $data['alpha']++;
                    break;
                case 'izin':
                    $data['izin']++;
                    break;
                case 'sakit':
                    $data['sakit']++;
                    break;
                case 'cuti':
                    $data['cuti']++;
                    break;
            }
        }

        return $data;
    }

    private function calculateOvertimeData($userId, $bulan, $tahun)
    {
        $overtimes = OvertimeRecord::where('user_id', $userId)
            ->byPeriod($bulan, $tahun)
            ->approved()
            ->get();

        $totalJam = $overtimes->sum('jam_lembur');
        $totalUpah = $overtimes->sum('total_upah_lembur');
        $upahPerJam = $totalJam > 0 ? $totalUpah / $totalJam : 0;

        return [
            'total_jam' => $totalJam,
            'total_upah' => $totalUpah,
            'upah_per_jam' => $upahPerJam
        ];
    }

    private function calculateDeductionData($userId, $bulan, $tahun)
    {
        $deductions = SalaryDeduction::where('user_id', $userId)
            ->byPeriod($bulan, $tahun)
            ->approved()
            ->get();

        $data = [
            'terlambat' => 0,
            'alpha' => 0,
            'sp' => 0,
            'lainnya' => 0
        ];

        foreach ($deductions as $deduction) {
            switch ($deduction->jenis) {
                case 'terlambat':
                    $data['terlambat'] += $deduction->jumlah_potongan;
                    break;
                case 'alpha':
                    $data['alpha'] += $deduction->jumlah_potongan;
                    break;
                case 'sp1':
                case 'sp2':
                case 'sp3':
                    $data['sp'] += $deduction->jumlah_potongan;
                    break;
                default:
                    $data['lainnya'] += $deduction->jumlah_potongan;
                    break;
            }
        }

        return $data;
    }

    public function show($id)
    {
        $salary = MonthlySalary::with(['user', 'creator', 'approver'])->findOrFail($id);

        // Ambil detail lembur dan potongan
        $overtimes = OvertimeRecord::where('user_id', $salary->user_id)
            ->byPeriod($salary->bulan, $salary->tahun)
            ->approved()
            ->get();

        $deductions = SalaryDeduction::where('user_id', $salary->user_id)
            ->byPeriod($salary->bulan, $salary->tahun)
            ->approved()
            ->get();

        return view('admin.salary.show', compact('salary', 'overtimes', 'deductions'));
    }

    public function approve($id)
    {
        $salary = MonthlySalary::findOrFail($id);

        $salary->update([
            'status' => 'approved',
            'tanggal_disetujui' => now(),
            'disetujui_oleh' => auth()->id()
        ]);

        return redirect()->back()->with('success', 'Slip gaji berhasil disetujui');
    }

    public function markAsPaid($id)
    {
        $salary = MonthlySalary::findOrFail($id);

        $salary->update([
            'status' => 'paid',
            'tanggal_dibayar' => now()
        ]);

        return redirect()->back()->with('success', 'Slip gaji berhasil ditandai sebagai dibayar');
    }

    public function printPdf($id)
    {
        $salary = MonthlySalary::with(['user', 'creator', 'approver'])->findOrFail($id);

        // Ambil pengaturan perusahaan
        $namaPerusahaan = Setting::getValue('nama_perusahaan', 'PT Absensi Pegawai');
        $alamatPerusahaan = Setting::getValue('alamat_perusahaan', 'Jalan Kolonel Masturi No 138');

        $pdf = PDF::loadView('admin.salary.pdf.slip', compact('salary', 'namaPerusahaan', 'alamatPerusahaan'));
        $pdf->setPaper('a4', 'portrait');

        return $pdf->download('slip-gaji-' . $salary->user->name . '-' . $salary->periode . '.pdf');
    }
}
