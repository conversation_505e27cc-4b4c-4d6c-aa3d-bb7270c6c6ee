@extends('layouts.app')

@section('styles')
<style>
    .table th {
        background: #f8f9fa;
        font-weight: 600;
        border-bottom: 2px solid #dee2e6;
        padding: 12px;
    }
    .table td {
        padding: 12px;
        vertical-align: middle;
    }
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .card-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 16px 20px;
    }
    .btn-primary {
        background: #0d6efd;
        border-color: #0d6efd;
    }
    .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
    }
    .form-control, .form-select {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 8px 12px;
    }
    .form-control:focus, .form-select:focus {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-0">Data Absensi Pegawai</h4>
                            <small class="text-muted">{{ \App\Facades\Tanggal::formatTanggalLengkap($tanggalFilter) }}</small>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-primary btn-sm me-2" onclick="exportToPDF()">
                                <i class="bi bi-file-earmark-pdf me-1"></i>Cetak PDF
                            </button>
                            <a href="{{ route('admin.rekap') }}" class="btn btn-primary btn-sm me-2">
                                <i class="bi bi-file-earmark-text me-1"></i>Rekap
                            </a>
                            <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-arrow-left me-1"></i>Kembali
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
                        </div>
                    @endif

                    <!-- Filter Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" action="{{ route('admin.absensi') }}" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">Tanggal</label>
                                    <input type="date" name="tanggal" class="form-control" value="{{ request('tanggal', date('Y-m-d')) }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Status</label>
                                    <select name="status" class="form-select">
                                        <option value="">Semua Status</option>
                                        <option value="hadir" {{ request('status') == 'hadir' ? 'selected' : '' }}>Hadir</option>
                                        <option value="alpha" {{ request('status') == 'alpha' ? 'selected' : '' }}>Alpha</option>
                                        <option value="izin" {{ request('status') == 'izin' ? 'selected' : '' }}>Izin</option>
                                        <option value="sakit" {{ request('status') == 'sakit' ? 'selected' : '' }}>Sakit</option>
                                        <option value="cuti" {{ request('status') == 'cuti' ? 'selected' : '' }}>Cuti</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Pegawai</label>
                                    <select name="user_id" class="form-select">
                                        <option value="">Semua Pegawai</option>
                                        @foreach(\App\Models\User::where('role', 'user')->orderBy('name')->get() as $user)
                                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="bi bi-search me-1"></i>Filter
                                    </button>
                                    <a href="{{ route('admin.absensi') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-clockwise me-1"></i>Reset
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center bg-success text-white">
                                <div class="card-body">
                                    <h4>{{ $totalHadir }}</h4>
                                    <p class="mb-0">Hadir</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center bg-danger text-white">
                                <div class="card-body">
                                    <h4>{{ $totalAlpha }}</h4>
                                    <p class="mb-0">Alpha</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center bg-warning text-white">
                                <div class="card-body">
                                    <h4>{{ $totalIzin }}</h4>
                                    <p class="mb-0">Izin/Sakit/Cuti</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center bg-primary text-white">
                                <div class="card-body">
                                    <h4>{{ $totalPegawai }}</h4>
                                    <p class="mb-0">Total Pegawai</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Table Section -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Pegawai</th>
                                    <th>Tanggal</th>
                                    <th>Jam Masuk</th>
                                    <th>Jam Keluar</th>
                                    <th>Status</th>
                                    <th>Keterangan</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($absen as $index => $a)
                                    <tr>
                                        <td>{{ $loop->iteration }}</td>
                                        <td>
                                            <strong>{{ $a->user->name }}</strong>
                                            @if($a->user->nik)
                                                <br><small class="text-muted">NIK: {{ $a->user->nik }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            {{ \App\Facades\Tanggal::formatTanggal($a->tanggal) }}
                                            <br><small class="text-muted">{{ \App\Facades\Tanggal::formatHari($a->tanggal) }}</small>
                                        </td>
                                        <td>
                                            @if(in_array($a->status, ['izin', 'sakit', 'cuti']))
                                                <span class="text-muted">-</span>
                                            @elseif($a->jam_masuk)
                                                {{ \Carbon\Carbon::parse($a->jam_masuk)->format('H:i') }}
                                                @php
                                                    try {
                                                        $jamMasuk = \App\Models\Setting::getValue('jam_masuk', '08:00:00');
                                                        $toleransi = (int)\App\Models\Setting::getValue('toleransi_keterlambatan', '15');

                                                        // Pastikan format jam masuk konsisten
                                                        if (strlen($jamMasuk) == 5) {
                                                            $jamMasuk .= ':00';
                                                        }

                                                        $jamMasukDateTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
                                                        $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransi);
                                                        $jamMasukUser = \Carbon\Carbon::parse($a->jam_masuk);
                                                        $isTerlambat = $jamMasukUser->gt($batasKeterlambatan);
                                                    } catch (\Exception $e) {
                                                        $isTerlambat = false;
                                                    }
                                                @endphp
                                                @if($isTerlambat)
                                                    @php
                                                        try {
                                                            $selisihMenit = $jamMasukUser->diffInMinutes($jamMasukDateTime);
                                                        } catch (\Exception $e) {
                                                            $selisihMenit = 0;
                                                        }
                                                    @endphp
                                                    <br><small class="text-danger">Terlambat {{ $selisihMenit }} menit</small>
                                                @else
                                                    <br><small class="text-success">Tepat waktu</small>
                                                @endif
                                            @else
                                                <span class="text-muted">Belum absen</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if(in_array($a->status, ['izin', 'sakit', 'cuti']))
                                                <span class="text-muted">-</span>
                                            @elseif($a->jam_keluar)
                                                {{ \Carbon\Carbon::parse($a->jam_keluar)->format('H:i') }}
                                                @if($a->jam_masuk)
                                                    @php
                                                        $jamMasuk = \Carbon\Carbon::parse($a->jam_masuk);
                                                        $jamKeluar = \Carbon\Carbon::parse($a->jam_keluar);
                                                        $durasi = $jamKeluar->diff($jamMasuk);
                                                    @endphp
                                                    <br><small class="text-info">{{ $durasi->h }}j {{ $durasi->i }}m</small>
                                                @endif
                                            @else
                                                <span class="text-muted">Belum pulang</span>
                                            @endif
                                        </td>
                                        <td>
                                            @php
                                                $statusColors = [
                                                    'hadir' => 'success',
                                                    'alpha' => 'danger',
                                                    'izin' => 'warning',
                                                    'sakit' => 'info',
                                                    'cuti' => 'primary'
                                                ];
                                                $color = $statusColors[$a->status] ?? 'secondary';
                                            @endphp
                                            <span class="badge bg-{{ $color }} status-badge">
                                                {{ ucfirst($a->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($a->keterangan)
                                                <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ $a->keterangan }}">
                                                    {{ $a->keterangan }}
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <i class="bi bi-calendar-x fs-1 text-muted"></i>
                                            <h5 class="text-muted mt-2">Tidak ada data absensi</h5>
                                            <p class="text-muted">Data absensi untuk tanggal yang dipilih tidak ditemukan</p>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($absen->hasPages())
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="text-muted small">
                                Menampilkan {{ $absen->firstItem() }} - {{ $absen->lastItem() }} dari {{ $absen->total() }} data
                            </div>
                            <div>
                                {{ $absen->appends(request()->query())->links() }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail -->
<div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detail Absensi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>



<script>
function showDetail(id) {
    // Show modal with absensi details
    $('#detailModal').modal('show');
    $('#detailContent').html('<div class="text-center"><div class="spinner-border" role="status"></div></div>');

    // You can add AJAX call here to load detail data
    setTimeout(() => {
        $('#detailContent').html('<p>Detail absensi akan ditampilkan di sini.</p>');
    }, 1000);
}



function exportToPDF() {
    const params = new URLSearchParams(window.location.search);
    const url = '{{ route("admin.absensi") }}?' + params.toString() + '&export=pdf';

    // Show loading
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Memproses...';
    btn.disabled = true;

    // Create a temporary link to download
    const link = document.createElement('a');
    link.href = url;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button after 2 seconds
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 2000);
}

// Auto refresh setiap 30 detik jika tidak ada filter
@if(!request()->hasAny(['tanggal', 'status', 'user_id']))
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 30000);
@endif
</script>
@endsection
