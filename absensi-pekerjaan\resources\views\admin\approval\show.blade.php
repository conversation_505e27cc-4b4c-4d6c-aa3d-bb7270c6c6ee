@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-lg border-0 rounded-lg">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Detail Pengajuan #{{ $pengajuan->id }}</h5>
                    <a href="{{ route('admin.approval.index') }}" class="btn btn-sm btn-light">
                        <i class="bi bi-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">Informasi Pengajuan</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th width="30%">ID Pengajuan</th>
                                            <td width="70%">{{ $pengajuan->id }}</td>
                                        </tr>
                                        <tr>
                                            <th>Tanggal</th>
                                            <td>
                                                {{ \Carbon\Carbon::parse($pengajuan->tanggal)->format('d F Y') }}
                                                ({{ \Carbon\Carbon::parse($pengajuan->tanggal)->locale('id')->dayName }})
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Jenis Pengajuan</th>
                                            <td>
                                                @if($pengajuan->status == 'izin')
                                                    <span class="badge bg-warning">Izin</span>
                                                @elseif($pengajuan->status == 'sakit')
                                                    <span class="badge bg-info">Sakit</span>
                                                @elseif($pengajuan->status == 'cuti')
                                                    <span class="badge bg-primary">Cuti</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>Status Approval</th>
                                            <td>
                                                @if($pengajuan->status_approval == 'pending')
                                                    <span class="badge bg-secondary">Menunggu</span>
                                                @elseif($pengajuan->status_approval == 'approved')
                                                    <span class="badge bg-success">Disetujui</span>
                                                @elseif($pengajuan->status_approval == 'rejected')
                                                    <span class="badge bg-danger">Ditolak</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @if($pengajuan->status_approval != 'pending')
                                            <tr>
                                                <th>Diproses Oleh</th>
                                                <td>{{ $pengajuan->approver->name ?? 'N/A' }}</td>
                                            </tr>
                                            <tr>
                                                <th>Waktu Diproses</th>
                                                <td>{{ $pengajuan->approval_at ? \Carbon\Carbon::parse($pengajuan->approval_at)->format('d F Y H:i:s') : 'N/A' }}</td>
                                            </tr>
                                            <tr>
                                                <th>Catatan</th>
                                                <td>{{ $pengajuan->approval_note ?: 'Tidak ada catatan' }}</td>
                                            </tr>
                                        @endif
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">Informasi Karyawan</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th width="30%">Nama</th>
                                            <td width="70%">{{ $pengajuan->user->name }}</td>
                                        </tr>
                                        <tr>
                                            <th>Email</th>
                                            <td>{{ $pengajuan->user->email }}</td>
                                        </tr>
                                        <tr>
                                            <th>Jabatan</th>
                                            <td>{{ $pengajuan->user->jabatan ?? 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Departemen</th>
                                            <td>{{ $pengajuan->user->departemen ?? 'N/A' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-4">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">Detail Pengajuan</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-4">
                                        <h6 class="fw-bold">Keterangan/Alasan:</h6>
                                        <p>{{ $pengajuan->keterangan }}</p>
                                    </div>

                                    @if($pengajuan->catatan)
                                        <div class="mb-4">
                                            <h6 class="fw-bold">Catatan Tambahan:</h6>
                                            <p>{{ $pengajuan->catatan }}</p>
                                        </div>
                                    @endif

                                    @if($pengajuan->tanda_tangan)
                                        <div class="mb-4">
                                            <h6 class="fw-bold">Tanda Tangan Digital:</h6>
                                            <div class="border p-3 bg-light text-center">
                                                <img src="{{ asset($pengajuan->tanda_tangan) }}" alt="Tanda Tangan" class="img-fluid" style="max-height: 150px;">
                                            </div>
                                        </div>
                                    @endif

                                    @if($pengajuan->dokumen)
                                        <div class="mb-4">
                                            <h6 class="fw-bold">Dokumen Pendukung:</h6>
                                            <div class="border p-3 bg-light">
                                                @php
                                                    $extension = pathinfo($pengajuan->dokumen, PATHINFO_EXTENSION);
                                                    $isPdf = strtolower($extension) === 'pdf';
                                                @endphp

                                                @if($isPdf)
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-file-earmark-pdf fs-1 text-danger me-3"></i>
                                                        <div>
                                                            <p class="mb-1">Dokumen PDF</p>
                                                            <div class="btn-group">
                                                                <a href="{{ asset('storage/' . $pengajuan->dokumen) }}" class="btn btn-sm btn-primary" target="_blank">
                                                                    <i class="bi bi-eye"></i> Lihat
                                                                </a>
                                                                <a href="{{ asset('storage/' . $pengajuan->dokumen) }}" class="btn btn-sm btn-success" download>
                                                                    <i class="bi bi-download"></i> Unduh
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @else
                                                    <div class="text-center">
                                                        <img src="{{ asset('storage/' . $pengajuan->dokumen) }}" alt="Dokumen Pendukung" class="img-fluid rounded" style="max-height: 300px;">
                                                        <div class="mt-2">
                                                            <a href="{{ asset('storage/' . $pengajuan->dokumen) }}" class="btn btn-sm btn-success" download>
                                                                <i class="bi bi-download"></i> Unduh Gambar
                                                            </a>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($pengajuan->status_approval == 'pending')
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card mb-4">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">Tindakan</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex gap-2">
                                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#approveModal">
                                                <i class="bi bi-check-circle"></i> Setujui Pengajuan
                                            </button>
                                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                                                <i class="bi bi-x-circle"></i> Tolak Pengajuan
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Approve Modal -->
                        <div class="modal fade" id="approveModal" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <form action="{{ route('admin.approval.approve', $pengajuan->id) }}" method="POST">
                                        @csrf
                                        <div class="modal-header bg-success text-white">
                                            <h5 class="modal-title" id="approveModalLabel">Setujui Pengajuan</h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Anda yakin ingin menyetujui pengajuan ini?</p>
                                            <div class="mb-3">
                                                <label for="approval_note" class="form-label">Catatan (Opsional)</label>
                                                <textarea class="form-control" id="approval_note" name="approval_note" rows="3"></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                            <button type="submit" class="btn btn-success">Setujui</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Reject Modal -->
                        <div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <form action="{{ route('admin.approval.reject', $pengajuan->id) }}" method="POST">
                                        @csrf
                                        <div class="modal-header bg-danger text-white">
                                            <h5 class="modal-title" id="rejectModalLabel">Tolak Pengajuan</h5>
                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Anda yakin ingin menolak pengajuan ini?</p>
                                            <div class="mb-3">
                                                <label for="approval_note" class="form-label">Alasan Penolakan <span class="text-danger">*</span></label>
                                                <textarea class="form-control" id="approval_note" name="approval_note" rows="3" required></textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                            <button type="submit" class="btn btn-danger">Tolak</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
