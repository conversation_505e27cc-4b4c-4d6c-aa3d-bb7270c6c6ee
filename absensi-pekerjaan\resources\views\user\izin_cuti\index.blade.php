@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Riwayat Pengajuan Izin/Cuti/Sakit</h5>
            <div>
                <a href="{{ route('dashboard') }}" class="btn btn-light btn-sm">
                    <i class="bi bi-house-door"></i> Kembali ke Dashboard
                </a>
                <a href="{{ route('izin-cuti.create') }}" class="btn btn-light btn-sm">
                    <i class="bi bi-plus-circle"></i> Buat Pengajuan Baru
                </a>
            </div>
        </div>
        <div class="card-body">
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif
            
            @if(session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif
            
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>No</th>
                            <th>Jenis</th>
                            <th>Tanggal</th>
                            <th>Durasi</th>
                            <th>Status</th>
                            <th>Keterangan</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($izinCutis as $index => $izinCuti)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>
                                    <span class="badge {{ 
                                        $izinCuti->jenis == 'izin' ? 'bg-info' : 
                                        ($izinCuti->jenis == 'sakit' ? 'bg-warning' : 'bg-primary') 
                                    }}">
                                        {{ ucfirst($izinCuti->jenis) }}
                                    </span>
                                </td>
                                <td>{{ \Carbon\Carbon::parse($izinCuti->tanggal_mulai)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($izinCuti->tanggal_selesai)->format('d/m/Y') }}</td>
                                <td>{{ $izinCuti->durasi }} hari</td>
                                <td>
                                    <span class="badge {{ 
                                        $izinCuti->status == 'approved' ? 'bg-success' : 
                                        ($izinCuti->status == 'rejected' ? 'bg-danger' : 'bg-secondary') 
                                    }}">
                                        {{ $izinCuti->status == 'approved' ? 'Disetujui' : 
                                           ($izinCuti->status == 'rejected' ? 'Ditolak' : 'Menunggu') 
                                        }}
                                    </span>
                                </td>
                                <td>{{ Str::limit($izinCuti->keterangan, 30) }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('izin-cuti.show', $izinCuti->id) }}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i> Detail
                                        </a>
                                        
                                        @if($izinCuti->status == 'pending')
                                            <form action="{{ route('izin-cuti.destroy', $izinCuti->id) }}" method="POST" onsubmit="return confirm('Apakah Anda yakin ingin membatalkan pengajuan ini?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="bi bi-trash"></i> Batalkan
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center">Belum ada pengajuan izin/cuti/sakit</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-center mt-4">
                {{ $izinCutis->links() }}
            </div>
        </div>
    </div>
</div>
@endsection
