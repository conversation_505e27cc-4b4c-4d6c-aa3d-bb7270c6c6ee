@extends('layouts.app')

@section('content')
<div class="modern-settings">
    <div class="container-fluid px-4">
        <!-- Header Section -->
        <div class="settings-header mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="header-content">
                        <h1 class="settings-title">
                            <i class="bi bi-gear-fill me-3"></i>
                            Pengaturan Sistem
                        </h1>
                        <p class="settings-subtitle">
                            Kelola pengaturan jam kerja dan konfigurasi sistem absensi
                        </p>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="header-actions">
                        <button type="button" class="btn btn-outline-primary" onclick="resetToDefault()">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            Reset Default
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        @if(session('success'))
        <div class="alert alert-success alert-modern alert-dismissible fade show" role="alert">
            <div class="alert-content">
                <i class="bi bi-check-circle-fill alert-icon"></i>
                <div class="alert-text">
                    <strong>Berhasil!</strong>
                    <p>{{ session('success') }}</p>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        @endif

        @if(session('error'))
        <div class="alert alert-danger alert-modern alert-dismissible fade show" role="alert">
            <div class="alert-content">
                <i class="bi bi-exclamation-triangle-fill alert-icon"></i>
                <div class="alert-text">
                    <strong>Gagal!</strong>
                    <p>{{ session('error') }}</p>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        @endif

        <!-- Settings Form -->
        <form action="{{ url('/admin/settings') }}" method="POST" class="settings-form">
            @csrf

            <!-- Work Schedule Section -->
            <div class="settings-section">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="bi bi-clock"></i>
                    </div>
                    <div class="section-info">
                        <h3>Jadwal Kerja</h3>
                        <p>Atur jam masuk, jam pulang, dan toleransi keterlambatan</p>
                    </div>
                </div>

                <div class="section-content">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <label for="jam_masuk" class="form-label-modern">
                                    <i class="bi bi-sunrise me-2"></i>
                                    Jam Masuk
                                </label>
                                <div class="input-group-modern">
                                    <input type="time" class="form-control-modern" id="jam_masuk" name="jam_masuk"
                                           value="{{ $settings['jam_masuk'] ?? '08:00' }}" required>
                                    <div class="input-icon">
                                        <i class="bi bi-clock"></i>
                                    </div>
                                </div>
                                <small class="form-help">Waktu mulai kerja karyawan</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <label for="jam_pulang" class="form-label-modern">
                                    <i class="bi bi-sunset me-2"></i>
                                    Jam Pulang
                                </label>
                                <div class="input-group-modern">
                                    <input type="time" class="form-control-modern" id="jam_pulang" name="jam_pulang"
                                           value="{{ $settings['jam_pulang'] ?? '17:00' }}" required>
                                    <div class="input-icon">
                                        <i class="bi bi-clock"></i>
                                    </div>
                                </div>
                                <small class="form-help">Waktu selesai kerja karyawan</small>
                            </div>
                        </div>
                    </div>

                    <div class="row g-4 mt-2">
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <label for="batas_absen_masuk" class="form-label-modern">
                                    <i class="bi bi-clock-history me-2"></i>
                                    Batas Absen Masuk
                                </label>
                                <div class="input-group-modern">
                                    <input type="time" class="form-control-modern" id="batas_absen_masuk" name="batas_absen_masuk"
                                           value="{{ $settings['batas_absen_masuk'] ?? '10:00' }}" required>
                                    <div class="input-icon">
                                        <i class="bi bi-clock"></i>
                                    </div>
                                </div>
                                <small class="form-help">Batas maksimal waktu untuk melakukan absen masuk</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <label for="minimum_jam_kerja" class="form-label-modern">
                                    <i class="bi bi-stopwatch me-2"></i>
                                    Minimum Jam Kerja
                                </label>
                                <div class="input-group-modern">
                                    <input type="number" class="form-control-modern" id="minimum_jam_kerja"
                                           name="minimum_jam_kerja" value="{{ $settings['minimum_jam_kerja'] ?? '8' }}"
                                           min="1" max="12" required>
                                    <div class="input-addon">jam</div>
                                </div>
                                <small class="form-help">Minimum jam kerja sebelum bisa absen keluar</small>
                            </div>
                        </div>
                    </div>

                    <div class="row g-4 mt-2">
                        <div class="col-md-6">
                            <div class="form-group-modern">
                                <label for="toleransi_keterlambatan" class="form-label-modern">
                                    <i class="bi bi-hourglass-split me-2"></i>
                                    Toleransi Keterlambatan
                                </label>
                                <div class="input-group-modern">
                                    <input type="number" class="form-control-modern" id="toleransi_keterlambatan"
                                           name="toleransi_keterlambatan" value="{{ $settings['toleransi_keterlambatan'] ?? '15' }}"
                                           min="0" max="60" required>
                                    <div class="input-addon">menit</div>
                                </div>
                                <small class="form-help">Batas waktu terlambat sebelum dianggap terlambat</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="work-hours-preview">
                                <h6>Preview Jam Kerja</h6>
                                <div class="preview-content">
                                    <div class="time-range">
                                        <span class="time-start" id="preview-start">08:00</span>
                                        <span class="time-separator">—</span>
                                        <span class="time-end" id="preview-end">17:00</span>
                                    </div>
                                    <div class="tolerance-info">
                                        <small>Toleransi: <span id="preview-tolerance">15</span> menit</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Work Days Section -->
            <div class="settings-section">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="bi bi-calendar-week"></i>
                    </div>
                    <div class="section-info">
                        <h3>Hari Kerja</h3>
                        <p>Pilih hari-hari kerja dalam seminggu</p>
                    </div>
                </div>

                <div class="section-content">
                    @php
                        $hariKerja = explode(',', $settings['hari_kerja'] ?? '1,2,3,4,5');
                        $hari = [
                            1 => ['nama' => 'Senin', 'short' => 'Sen', 'icon' => 'bi-calendar-day'],
                            2 => ['nama' => 'Selasa', 'short' => 'Sel', 'icon' => 'bi-calendar-day'],
                            3 => ['nama' => 'Rabu', 'short' => 'Rab', 'icon' => 'bi-calendar-day'],
                            4 => ['nama' => 'Kamis', 'short' => 'Kam', 'icon' => 'bi-calendar-day'],
                            5 => ['nama' => 'Jumat', 'short' => 'Jum', 'icon' => 'bi-calendar-day'],
                            6 => ['nama' => 'Sabtu', 'short' => 'Sab', 'icon' => 'bi-calendar-day'],
                            7 => ['nama' => 'Minggu', 'short' => 'Min', 'icon' => 'bi-calendar-day'],
                        ];
                    @endphp

                    <div class="work-days-grid">
                        @foreach($hari as $key => $data)
                        <div class="work-day-item">
                            <input type="checkbox" class="work-day-checkbox" id="hari_{{ $key }}"
                                   name="hari_kerja[]" value="{{ $key }}"
                                   {{ in_array($key, $hariKerja) ? 'checked' : '' }}>
                            <label class="work-day-label" for="hari_{{ $key }}">
                                <div class="day-icon">
                                    <i class="bi {{ $data['icon'] }}"></i>
                                </div>
                                <div class="day-name">{{ $data['nama'] }}</div>
                                <div class="day-short">{{ $data['short'] }}</div>
                                <div class="check-indicator">
                                    <i class="bi bi-check-lg"></i>
                                </div>
                            </label>
                        </div>
                        @endforeach
                    </div>

                    <div class="work-days-summary">
                        <div class="summary-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <span id="selected-days-count">{{ count($hariKerja) }}</span> hari kerja dipilih
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="settings-actions">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="action-info">
                            <i class="bi bi-shield-check text-success me-2"></i>
                            <small class="text-muted">Pengaturan akan diterapkan untuk semua karyawan</small>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <button type="button" class="btn btn-outline-secondary me-2" onclick="previewChanges()">
                            <i class="bi bi-eye me-2"></i>
                            Preview
                        </button>
                        <button type="submit" class="btn btn-primary btn-save">
                            <i class="bi bi-check-lg me-2"></i>
                            Simpan Pengaturan
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
/* Modern Settings Styles */
.modern-settings {
    background: linear-gradient(135deg, #f8f9fc 0%, #e9ecef 100%);
    min-height: 100vh;
    padding: 20px 0;
}

/* Header Styles */
.settings-header {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.settings-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.settings-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 10px 0 0;
}

/* Alert Styles */
.alert-modern {
    border: none;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.alert-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.alert-icon {
    font-size: 1.5rem;
}

.alert-text strong {
    display: block;
    margin-bottom: 5px;
}

.alert-text p {
    margin: 0;
    opacity: 0.9;
}

/* Settings Form */
.settings-form {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Settings Section */
.settings-section {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.settings-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.section-header {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    color: white;
    padding: 25px 30px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.section-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.section-info h3 {
    margin: 0 0 5px;
    font-size: 1.5rem;
    font-weight: 700;
}

.section-info p {
    margin: 0;
    opacity: 0.9;
    font-size: 1rem;
}

.section-content {
    padding: 30px;
}

/* Form Groups */
.form-group-modern {
    margin-bottom: 25px;
}

.form-label-modern {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #2d3436;
    margin-bottom: 10px;
    font-size: 1rem;
}

.input-group-modern {
    position: relative;
    display: flex;
    align-items: center;
}

.form-control-modern {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-control-modern:focus {
    outline: none;
    border-color: #4e73df;
    background: white;
    box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.1);
}

.input-icon {
    position: absolute;
    right: 15px;
    color: #6c757d;
    font-size: 1.1rem;
    pointer-events: none;
}

.input-addon {
    position: absolute;
    right: 15px;
    background: #4e73df;
    color: white;
    padding: 5px 10px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
}

.form-help {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 8px;
    display: block;
}

/* Work Hours Preview */
.work-hours-preview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.work-hours-preview h6 {
    margin: 0 0 15px;
    font-weight: 600;
    opacity: 0.9;
}

.time-range {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.time-separator {
    margin: 0 10px;
    opacity: 0.7;
}

.tolerance-info {
    opacity: 0.8;
}

/* Work Days Grid */
.work-days-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.work-day-item {
    position: relative;
}

.work-day-checkbox {
    display: none;
}

.work-day-label {
    display: block;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 20px 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.work-day-label:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.work-day-checkbox:checked + .work-day-label {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border-color: #4e73df;
    color: white;
}

.day-icon {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.day-name {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 3px;
}

.day-short {
    font-size: 0.8rem;
    opacity: 0.7;
}

.check-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: white;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.work-day-checkbox:checked + .work-day-label .check-indicator {
    opacity: 1;
    transform: scale(1);
}

/* Work Days Summary */
.work-days-summary {
    background: rgba(78, 115, 223, 0.1);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
}

.summary-info {
    color: #4e73df;
    font-weight: 600;
}

/* Settings Actions */
.settings-actions {
    background: white;
    border-radius: 20px;
    padding: 25px 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.action-info {
    display: flex;
    align-items: center;
}

.btn-save {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(78, 115, 223, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-title {
        font-size: 2rem;
    }

    .section-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .work-days-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .settings-actions .row {
        flex-direction: column;
        gap: 15px;
    }

    .settings-actions .col-md-6 {
        text-align: center !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update preview saat input berubah
    const jamMasukInput = document.getElementById('jam_masuk');
    const jamPulangInput = document.getElementById('jam_pulang');
    const toleransiInput = document.getElementById('toleransi_keterlambatan');
    const workDayCheckboxes = document.querySelectorAll('.work-day-checkbox');

    // Update preview jam kerja
    function updatePreview() {
        // Ambil nilai dari input tanpa mengubah nilai input itu sendiri
        const jamMasuk = jamMasukInput.value;
        const jamPulang = jamPulangInput.value;
        const toleransi = toleransiInput.value;

        // Update preview hanya jika ada nilai
        if (jamMasuk) {
            document.getElementById('preview-start').textContent = jamMasuk;
        }
        if (jamPulang) {
            document.getElementById('preview-end').textContent = jamPulang;
        }
        if (toleransi) {
            document.getElementById('preview-tolerance').textContent = toleransi;
        }
    }

    // Update jumlah hari kerja
    function updateWorkDaysCount() {
        const checkedDays = document.querySelectorAll('.work-day-checkbox:checked').length;
        document.getElementById('selected-days-count').textContent = checkedDays;
    }

    // Fungsi validasi waktu
    function validateTime() {
        const jamMasuk = jamMasukInput.value;
        const jamPulang = jamPulangInput.value;

        if (jamMasuk && jamPulang) {
            if (jamMasuk >= jamPulang) {
                alert('Jam pulang harus lebih besar dari jam masuk!');
                // Reset ke nilai sebelumnya atau default
                if (jamMasukInput.dataset.previousValue) {
                    jamMasukInput.value = jamMasukInput.dataset.previousValue;
                }
                if (jamPulangInput.dataset.previousValue) {
                    jamPulangInput.value = jamPulangInput.dataset.previousValue;
                }
                return false;
            }
        }
        return true;
    }

    // Event listeners dengan validasi
    jamMasukInput.addEventListener('focus', function() {
        this.dataset.previousValue = this.value;
    });

    jamMasukInput.addEventListener('change', function() {
        if (validateTime()) {
            this.dataset.previousValue = this.value;
            updatePreview();
        }
    });

    jamPulangInput.addEventListener('focus', function() {
        this.dataset.previousValue = this.value;
    });

    jamPulangInput.addEventListener('change', function() {
        if (validateTime()) {
            this.dataset.previousValue = this.value;
            updatePreview();
        }
    });

    toleransiInput.addEventListener('input', function() {
        updatePreview();
    });

    workDayCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateWorkDaysCount);
    });

    // Inisialisasi dengan delay untuk memastikan DOM sudah siap
    setTimeout(() => {
        updatePreview();
        updateWorkDaysCount();
    }, 100);
});

// Fungsi reset ke default
function resetToDefault() {
    if (confirm('Apakah Anda yakin ingin mereset semua pengaturan ke nilai default?')) {
        // Reset input values
        const jamMasukInput = document.getElementById('jam_masuk');
        const jamPulangInput = document.getElementById('jam_pulang');
        const toleransiInput = document.getElementById('toleransi_keterlambatan');

        jamMasukInput.value = '08:00';
        jamPulangInput.value = '17:00';
        toleransiInput.value = '15';

        // Reset hari kerja ke Senin-Jumat
        document.querySelectorAll('.work-day-checkbox').forEach((checkbox, index) => {
            checkbox.checked = index < 5; // Senin-Jumat (index 0-4)
        });

        // Update preview setelah reset
        setTimeout(() => {
            document.getElementById('preview-start').textContent = '08:00';
            document.getElementById('preview-end').textContent = '17:00';
            document.getElementById('preview-tolerance').textContent = '15';
            document.getElementById('selected-days-count').textContent = '5';
        }, 50);

        // Trigger change events untuk memastikan preview terupdate
        jamMasukInput.dispatchEvent(new Event('change'));
        jamPulangInput.dispatchEvent(new Event('change'));
        toleransiInput.dispatchEvent(new Event('input'));
    }
}

// Fungsi preview perubahan
function previewChanges() {
    const jamMasuk = document.getElementById('jam_masuk').value;
    const jamPulang = document.getElementById('jam_pulang').value;
    const toleransi = document.getElementById('toleransi_keterlambatan').value;
    const selectedDays = Array.from(document.querySelectorAll('.work-day-checkbox:checked'))
        .map(cb => cb.nextElementSibling.querySelector('.day-name').textContent);

    const previewText = `
Pengaturan yang akan disimpan:
• Jam Masuk: ${jamMasuk}
• Jam Pulang: ${jamPulang}
• Toleransi Keterlambatan: ${toleransi} menit
• Hari Kerja: ${selectedDays.join(', ')}

Pengaturan ini akan berlaku untuk semua karyawan.
    `;

    alert(previewText);
}

// Animasi saat form submit
document.querySelector('.settings-form').addEventListener('submit', function(e) {
    const submitBtn = document.querySelector('.btn-save');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Menyimpan...';
    submitBtn.disabled = true;

    // Jika ada error, kembalikan tombol ke state semula
    setTimeout(() => {
        if (submitBtn.disabled) {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }, 5000);
});
</script>

@endsection
