<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\IzinCuti;
use App\Models\User;
use App\Models\Setting;
use App\Models\Absensi;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class IzinCutiController extends Controller
{
    public function index(Request $request)
    {
        try {
            $query = IzinCuti::with(['user', 'approver']);

            // Filter berdasarkan jenis
            if ($request->filled('jenis')) {
                $query->where('jenis', $request->jenis);
            }

            // Filter berdasarkan status
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // Filter berdasarkan user
            if ($request->filled('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            // Filter berdasarkan tanggal
            if ($request->filled('tanggal_mulai')) {
                $query->where('tanggal_mulai', '>=', $request->tanggal_mulai);
            }

            if ($request->filled('tanggal_selesai')) {
                $query->where('tanggal_selesai', '<=', $request->tanggal_selesai);
            }

            $izinCutis = $query->orderBy('created_at', 'desc')->paginate(10);
            $users = User::where('role', 'user')->get();

            // Debug info
            // dd($izinCutis);

            return view('admin.izin_cuti.index', compact('izinCutis', 'users'));
        } catch (\Exception $e) {
            return redirect()->route('admin.dashboard')->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    public function show($id)
    {
        try {
            $izinCuti = IzinCuti::with(['user', 'approver'])->findOrFail($id);
            return view('admin.izin_cuti.show', compact('izinCuti'));
        } catch (\Exception $e) {
            return redirect()->route('admin.izin-cuti.index')->with('error', 'Data tidak ditemukan: ' . $e->getMessage());
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $request->validate([
                'status' => 'required|in:approved,rejected',
                'catatan_admin' => 'nullable|string',
            ]);

            $izinCuti = IzinCuti::findOrFail($id);

            // Update status
            $izinCuti->status = $request->status;
            $izinCuti->catatan_admin = $request->catatan_admin;
            $izinCuti->approved_by = auth()->id();
            $izinCuti->approval_at = now();
            $izinCuti->save();

            // Jika status disetujui, buat entri absensi
            if ($request->status == 'approved') {
                // Buat entri absensi untuk setiap hari dalam rentang tanggal
                $tanggalMulai = Carbon::parse($izinCuti->tanggal_mulai);
                $tanggalSelesai = Carbon::parse($izinCuti->tanggal_selesai);

                // Loop untuk setiap hari dalam rentang
                for ($tanggal = $tanggalMulai->copy(); $tanggal->lte($tanggalSelesai); $tanggal->addDay()) {
                    // Cek apakah sudah ada absensi untuk tanggal dan user ini
                    $existingAbsensi = Absensi::where('user_id', $izinCuti->user_id)
                        ->where('tanggal', $tanggal->format('Y-m-d'))
                        ->first();

                    // Jika belum ada, buat entri baru
                    if (!$existingAbsensi) {
                        Absensi::create([
                            'user_id' => $izinCuti->user_id,
                            'tanggal' => $tanggal->format('Y-m-d'),
                            'status' => $izinCuti->jenis, // izin, sakit, atau cuti
                            'keterangan' => $izinCuti->keterangan,
                            'pengajuan_id' => $izinCuti->id,
                            'status_approval' => 'approved',
                            'approval_at' => now(),
                            'approved_by' => auth()->id()
                        ]);
                    } else {
                        // Jika sudah ada absensi (misalnya alpha), update statusnya
                        $oldStatus = $existingAbsensi->status;
                        $existingAbsensi->update([
                            'status' => $izinCuti->jenis,
                            'keterangan' => $izinCuti->keterangan,
                            'pengajuan_id' => $izinCuti->id,
                            'status_approval' => 'approved',
                            'approval_at' => now(),
                            'approved_by' => auth()->id()
                        ]);

                        // Hapus potongan gaji jika status berubah dari alpha/terlambat ke izin/cuti/sakit
                        if (in_array($oldStatus, ['alpha', 'terlambat'])) {
                            \App\Models\SalaryDeduction::where('absensi_id', $existingAbsensi->id)
                                ->whereIn('jenis', ['alpha', 'terlambat'])
                                ->delete();
                        }
                    }
                }

                return redirect()->route('admin.izin-cuti.index')->with('success', 'Pengajuan berhasil disetujui dan entri absensi telah dibuat.');
            } else {
                return redirect()->route('admin.izin-cuti.index')->with('success', 'Pengajuan berhasil ditolak.');
            }
        } catch (\Exception $e) {
            return redirect()->route('admin.izin-cuti.index')->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Cetak surat izin/cuti/sakit dalam format PDF
     */
    public function cetak($id)
    {
        try {
            $izin = IzinCuti::with(['user', 'approver'])->findOrFail($id);

            // Ambil pengaturan perusahaan
            $namaPerusahaan = Setting::getValue('nama_perusahaan', 'PT. Absensi Indonesia');
            $alamatPerusahaan = Setting::getValue('alamat_perusahaan', 'Jl. Contoh No. 123, Jakarta');

            // Buat PDF
            $pdf = PDF::loadView('admin.izin_cuti.cetak', compact('izin', 'namaPerusahaan', 'alamatPerusahaan'));
            $pdf->setPaper('a4', 'portrait');

            // Download PDF
            $jenis = $izin->jenis ?? $izin->status ?? 'izin';
            $userName = $izin->user ? $izin->user->name : 'pegawai';
            return $pdf->download('surat-' . $jenis . '-' . $userName . '-' . date('Y-m-d') . '.pdf');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Gagal mencetak PDF: ' . $e->getMessage());
        }
    }
}
