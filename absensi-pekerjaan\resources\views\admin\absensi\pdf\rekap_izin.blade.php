<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title><PERSON>ka<PERSON>/Cuti/Sakit</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h2 {
            margin: 0;
            padding: 0;
            font-size: 18px;
        }
        .header p {
            margin: 5px 0;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: right;
        }
        .page-break {
            page-break-after: always;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .badge {
            display: inline-block;
            padding: 3px 6px;
            font-size: 10px;
            font-weight: bold;
            border-radius: 3px;
        }
        .badge-warning {
            background-color: #fcf8e3;
            color: #8a6d3b;
        }
        .badge-info {
            background-color: #d9edf7;
            color: #31708f;
        }
        .badge-primary {
            background-color: #d9edf7;
            color: #31708f;
        }
        .badge-success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .badge-danger {
            background-color: #f2dede;
            color: #a94442;
        }
        .badge-secondary {
            background-color: #e9ecef;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>{{ $namaPerusahaan }}</h2>
        <p>{{ $alamatPerusahaan }}</p>
        <h3>REKAP PENGAJUAN IZIN/CUTI/SAKIT</h3>
        <p>Periode: {{ \Carbon\Carbon::parse($tanggalMulai)->format('d F Y') }} - {{ \Carbon\Carbon::parse($tanggalAkhir)->format('d F Y') }}</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="15%">Karyawan</th>
                <th width="10%">Jenis</th>
                <th width="15%">Tanggal</th>
                <th width="5%">Durasi</th>
                <th width="10%">Status</th>
                <th width="10%">Tanggal Approval</th>
                <th width="10%">Disetujui Oleh</th>
                <th width="20%">Keterangan</th>
            </tr>
        </thead>
        <tbody>
            @forelse($pengajuanGrup as $index => $item)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $item->user->name }}</td>
                    <td>
                        @if($item->status == 'izin')
                            <span class="badge badge-warning">Izin</span>
                        @elseif($item->status == 'sakit')
                            <span class="badge badge-info">Sakit</span>
                        @elseif($item->status == 'cuti')
                            <span class="badge badge-primary">Cuti</span>
                        @endif
                    </td>
                    <td>
                        @if(isset($item->tanggal_mulai) && isset($item->tanggal_selesai))
                            {{ \Carbon\Carbon::parse($item->tanggal_mulai)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($item->tanggal_selesai)->format('d/m/Y') }}
                        @else
                            {{ \Carbon\Carbon::parse($item->tanggal_awal ?? $item->tanggal)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($item->tanggal_akhir ?? $item->tanggal)->format('d/m/Y') }}
                        @endif
                    </td>
                    <td>{{ $item->jumlah_hari ?? '1' }} hari</td>
                    <td>
                        @if($item->status_approval == 'pending')
                            <span class="badge badge-secondary">Menunggu</span>
                        @elseif($item->status_approval == 'approved')
                            <span class="badge badge-success">Disetujui</span>
                        @elseif($item->status_approval == 'rejected')
                            <span class="badge badge-danger">Ditolak</span>
                        @endif
                    </td>
                    <td>
                        @if($item->approval_at)
                            {{ \Carbon\Carbon::parse($item->approval_at)->format('d/m/Y H:i') }}
                        @else
                            -
                        @endif
                    </td>
                    <td>
                        {{ $item->approver->name ?? '-' }}
                    </td>
                    <td>
                        {{ $item->keterangan }}
                        @if($item->approval_note)
                            <br><small>Catatan: {{ $item->approval_note }}</small>
                        @endif
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="9" class="text-center">Tidak ada data pengajuan</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    
    <div class="footer">
        <p>Dicetak pada: {{ date('d F Y H:i:s') }}</p>
        <p>Oleh: {{ Auth::user()->name }}</p>
    </div>
</body>
</html>
