<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Absensi;
use App\Models\SalaryDeduction;
use App\Models\Setting;
use Carbon\Carbon;

class ProcessLatenessDeductions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:process-lateness {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process lateness deductions and warning letters';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $date = $this->argument('date') ? Carbon::parse($this->argument('date')) : Carbon::yesterday();
        
        $this->info("Processing lateness for: {$date->format('Y-m-d')} ({$date->format('l')})");

        // Ambil setting jam masuk dan toleransi
        $jamMasuk = Setting::getValue('jam_masuk', '08:00:00');
        $toleransiKeterlambatan = (int)Setting::getValue('toleransi_keterlambatan', '15');
        
        // Ambil semua absensi hadir pada tanggal tersebut
        $absensis = Absensi::where('status', 'hadir')
            ->whereDate('tanggal', $date->format('Y-m-d'))
            ->whereNotNull('jam_masuk')
            ->with('user')
            ->get();

        $lateCount = 0;
        $processedCount = 0;

        foreach ($absensis as $absensi) {
            $processedCount++;
            
            if ($this->isLate($absensi, $jamMasuk, $toleransiKeterlambatan)) {
                // Cek apakah sudah ada potongan untuk keterlambatan ini
                $existingDeduction = SalaryDeduction::where('user_id', $absensi->user_id)
                    ->where('absensi_id', $absensi->id)
                    ->where('jenis', 'terlambat')
                    ->first();

                if (!$existingDeduction) {
                    $this->createLatenessDeduction($absensi, $date);
                    $this->checkAndUpdateLatenessWarnings($absensi->user, $date);
                    $lateCount++;
                    
                    $lateMinutes = $this->calculateLateMinutes($absensi, $jamMasuk);
                    $this->line("  - {$absensi->user->name}: Late by {$lateMinutes} minutes - Deduction applied");
                } else {
                    $this->line("  - {$absensi->user->name}: Already has lateness deduction");
                }
            }
        }

        $this->info("\nSummary:");
        $this->info("- Total attendance records processed: {$processedCount}");
        $this->info("- Late employees with deductions: {$lateCount}");
    }

    private function isLate($absensi, $jamMasuk, $toleransiKeterlambatan)
    {
        try {
            $jamMasukDateTime = Carbon::createFromFormat('H:i:s', $jamMasuk);
            $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransiKeterlambatan);
            $jamMasukUser = Carbon::parse($absensi->jam_masuk);
            
            return $jamMasukUser->gt($batasKeterlambatan);
        } catch (\Exception $e) {
            $this->error("Error checking lateness for {$absensi->user->name}: " . $e->getMessage());
            return false;
        }
    }

    private function calculateLateMinutes($absensi, $jamMasuk)
    {
        try {
            $jamMasukDateTime = Carbon::createFromFormat('H:i:s', $jamMasuk);
            $jamMasukUser = Carbon::parse($absensi->jam_masuk);
            
            return $jamMasukDateTime->diffInMinutes($jamMasukUser);
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function createLatenessDeduction($absensi, $date)
    {
        $potonganTerlambat = (float)Setting::getValue('potongan_terlambat', 50000);
        
        SalaryDeduction::create([
            'user_id' => $absensi->user_id,
            'tanggal' => $date->format('Y-m-d'),
            'bulan' => $date->month,
            'tahun' => $date->year,
            'jenis' => 'terlambat',
            'jumlah_potongan' => $potonganTerlambat,
            'keterangan' => 'Potongan keterlambatan pada ' . $date->format('d/m/Y'),
            'absensi_id' => $absensi->id,
            'status' => 'approved',
            'dibuat_oleh' => 1, // System
            'disetujui_oleh' => 1 // Auto approved
        ]);
    }

    private function checkAndUpdateLatenessWarnings($user, $date)
    {
        // Hitung total keterlambatan dalam bulan ini
        $lateThisMonth = SalaryDeduction::where('user_id', $user->id)
            ->where('jenis', 'terlambat')
            ->whereMonth('tanggal', $date->month)
            ->whereYear('tanggal', $date->year)
            ->count();

        // Ambil batas untuk SP
        $batasTerlambatSP1 = (int)Setting::getValue('batas_terlambat_sp1', 3);
        $batasTerlambatSP2 = (int)Setting::getValue('batas_terlambat_sp2', 5);
        $batasTerlambatSP3 = (int)Setting::getValue('batas_terlambat_sp3', 7);

        $currentSP = $user->jumlah_sp;

        // Cek apakah perlu SP baru berdasarkan keterlambatan
        if ($lateThisMonth >= $batasTerlambatSP3 && $currentSP < 3) {
            $this->issueSP($user, 'sp3', $date, 'keterlambatan');
        } elseif ($lateThisMonth >= $batasTerlambatSP2 && $currentSP < 2) {
            $this->issueSP($user, 'sp2', $date, 'keterlambatan');
        } elseif ($lateThisMonth >= $batasTerlambatSP1 && $currentSP < 1) {
            $this->issueSP($user, 'sp1', $date, 'keterlambatan');
        }
    }

    private function issueSP($user, $spType, $date, $reason)
    {
        $spNumber = (int)str_replace('sp', '', $spType);
        
        // Update user SP
        $user->update([
            'jumlah_sp' => $spNumber,
            'tanggal_sp_terakhir' => $date->format('Y-m-d')
        ]);

        // Buat potongan gaji untuk SP
        $potonganSP = (float)Setting::getValue('potongan_' . $spType, 100000);
        
        SalaryDeduction::create([
            'user_id' => $user->id,
            'tanggal' => $date->format('Y-m-d'),
            'bulan' => $date->month,
            'tahun' => $date->year,
            'jenis' => $spType,
            'jumlah_potongan' => $potonganSP,
            'keterangan' => 'Surat Peringatan ' . $spNumber . ' karena ' . $reason . ' berulang',
            'status' => 'approved',
            'dibuat_oleh' => 1,
            'disetujui_oleh' => 1
        ]);

        $this->warn("  - {$user->name}: Issued {$spType} (Total {$reason} this month: " . 
                   SalaryDeduction::where('user_id', $user->id)
                                  ->where('jenis', 'terlambat')
                                  ->whereMonth('tanggal', $date->month)
                                  ->whereYear('tanggal', $date->year)
                                  ->count() . ")");
    }
}
