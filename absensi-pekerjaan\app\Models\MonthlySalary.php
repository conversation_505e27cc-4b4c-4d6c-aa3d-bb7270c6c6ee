<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class MonthlySalary extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'bulan',
        'tahun',
        'gaji_pokok',
        'tunjangan_transport',
        'tunjangan_makan',
        'tunjangan_lainnya',
        'total_jam_lembur',
        'upah_lembur_per_jam',
        'total_upah_lembur',
        'total_hadir',
        'total_terlambat',
        'total_alpha',
        'total_izin',
        'total_sakit',
        'total_cuti',
        'potongan_terlambat',
        'potongan_alpha',
        'potongan_sp',
        'potongan_lainnya',
        'total_pendapatan',
        'total_potongan',
        'gaji_bersih',
        'status',
        'catatan',
        'tanggal_dibuat',
        'tanggal_disetujui',
        'tanggal_dibayar',
        'dibuat_oleh',
        'disetujui_oleh'
    ];

    protected $casts = [
        'tanggal_dibuat' => 'datetime',
        'tanggal_disetujui' => 'datetime',
        'tanggal_dibayar' => 'datetime',
        'gaji_pokok' => 'decimal:2',
        'tunjangan_transport' => 'decimal:2',
        'tunjangan_makan' => 'decimal:2',
        'tunjangan_lainnya' => 'decimal:2',
        'total_jam_lembur' => 'decimal:2',
        'upah_lembur_per_jam' => 'decimal:2',
        'total_upah_lembur' => 'decimal:2',
        'potongan_terlambat' => 'decimal:2',
        'potongan_alpha' => 'decimal:2',
        'potongan_sp' => 'decimal:2',
        'potongan_lainnya' => 'decimal:2',
        'total_pendapatan' => 'decimal:2',
        'total_potongan' => 'decimal:2',
        'gaji_bersih' => 'decimal:2',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'dibuat_oleh');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'disetujui_oleh');
    }

    // Accessors
    public function getPeriodeAttribute()
    {
        $bulanIndonesia = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];
        
        return $bulanIndonesia[$this->bulan] . ' ' . $this->tahun;
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'draft' => 'bg-secondary',
            'approved' => 'bg-success',
            'paid' => 'bg-primary'
        ];
        
        return $badges[$this->status] ?? 'bg-secondary';
    }

    public function getStatusTextAttribute()
    {
        $texts = [
            'draft' => 'Draft',
            'approved' => 'Disetujui',
            'paid' => 'Dibayar'
        ];
        
        return $texts[$this->status] ?? 'Draft';
    }

    // Methods
    public function calculateTotals()
    {
        $this->total_pendapatan = $this->gaji_pokok + 
                                 $this->tunjangan_transport + 
                                 $this->tunjangan_makan + 
                                 $this->tunjangan_lainnya + 
                                 $this->total_upah_lembur;

        $this->total_potongan = $this->potongan_terlambat + 
                               $this->potongan_alpha + 
                               $this->potongan_sp + 
                               $this->potongan_lainnya;

        $this->gaji_bersih = $this->total_pendapatan - $this->total_potongan;
    }

    // Scopes
    public function scopeByPeriod($query, $bulan, $tahun)
    {
        return $query->where('bulan', $bulan)->where('tahun', $tahun);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeCurrentMonth($query)
    {
        $now = Carbon::now();
        return $query->where('bulan', $now->month)->where('tahun', $now->year);
    }
}
