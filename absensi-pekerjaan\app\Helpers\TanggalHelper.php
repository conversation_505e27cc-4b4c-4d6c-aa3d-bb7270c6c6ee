<?php

namespace App\Helpers;

use Carbon\Carbon;

class TanggalHelper
{
    /**
     * Daftar nama hari dalam bahasa Indonesia
     */
    protected static $namaHari = [
        'Sunday'    => 'Minggu',
        'Monday'    => 'Senin',
        'Tuesday'   => 'Selasa',
        'Wednesday' => 'Rabu',
        'Thursday'  => 'Kamis',
        'Friday'    => 'Jumat',
        'Saturday'  => 'Sabtu'
    ];

    /**
     * Daftar nama bulan dalam bahasa Indonesia
     */
    protected static $namaBulan = [
        'January'   => 'Januari',
        'February'  => 'Februari',
        'March'     => 'Maret',
        'April'     => 'April',
        'May'       => 'Mei',
        'June'      => 'Juni',
        'July'      => 'Juli',
        'August'    => 'Agustus',
        'September' => 'September',
        'October'   => 'Oktober',
        'November'  => 'November',
        'December'  => 'Desember'
    ];

    /**
     * Format tanggal lengkap dengan nama hari dan bulan dalam bahasa Indonesia
     *
     * @param string|Carbon $tanggal
     * @return string
     */
    public static function formatTanggalLengkap($tanggal)
    {
        if (!$tanggal) {
            return '-';
        }

        $carbon = $tanggal instanceof Carbon ? $tanggal : Carbon::parse($tanggal);

        $namaHari = self::$namaHari[$carbon->format('l')];
        $namaBulan = self::$namaBulan[$carbon->format('F')];

        return $namaHari . ', ' . $carbon->format('d') . ' ' . $namaBulan . ' ' . $carbon->format('Y');
    }

    /**
     * Format tanggal dengan nama bulan dalam bahasa Indonesia
     *
     * @param string|Carbon $tanggal
     * @return string
     */
    public static function formatTanggal($tanggal)
    {
        if (!$tanggal) {
            return '-';
        }

        $carbon = $tanggal instanceof Carbon ? $tanggal : Carbon::parse($tanggal);

        $namaBulan = self::$namaBulan[$carbon->format('F')];

        return $carbon->format('d') . ' ' . $namaBulan . ' ' . $carbon->format('Y');
    }

    /**
     * Format nama hari dalam bahasa Indonesia
     *
     * @param string|Carbon $tanggal
     * @return string
     */
    public static function formatHari($tanggal)
    {
        if (!$tanggal) {
            return '-';
        }

        $carbon = $tanggal instanceof Carbon ? $tanggal : Carbon::parse($tanggal);

        return self::$namaHari[$carbon->format('l')];
    }

    /**
     * Format jam
     *
     * @param string|Carbon $jam
     * @return string
     */
    public static function formatJam($jam)
    {
        if (!$jam) {
            return '-';
        }

        $carbon = $jam instanceof Carbon ? $jam : Carbon::parse($jam);

        return $carbon->format('H:i:s');
    }

    /**
     * Format tanggal dan jam lengkap
     *
     * @param string|Carbon $tanggalJam
     * @return string
     */
    public static function formatTanggalJam($tanggalJam)
    {
        if (!$tanggalJam) {
            return '-';
        }

        $carbon = $tanggalJam instanceof Carbon ? $tanggalJam : Carbon::parse($tanggalJam);

        $namaHari = self::$namaHari[$carbon->format('l')];
        $namaBulan = self::$namaBulan[$carbon->format('F')];

        return $namaHari . ', ' . $carbon->format('d') . ' ' . $namaBulan . ' ' . $carbon->format('Y') . ' ' . $carbon->format('H:i:s');
    }
}
