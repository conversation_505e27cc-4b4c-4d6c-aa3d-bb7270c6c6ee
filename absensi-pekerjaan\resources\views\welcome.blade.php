<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Absensi App - Sistem Absensi Modern</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sistem absensi modern dengan fitur foto dan tanda tangan digital untuk perusahaan <PERSON>">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="https://cdn-icons-png.flaticon.com/512/2910/2910791.png">

    <!-- Google Font -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap + Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" />

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />

    <!-- Swiper Slider -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css" />

    <!-- Signature Pad -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/signature_pad/1.5.3/signature_pad.min.js"></script>

    <!-- Particles.js -->
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>

    <!-- Three.js for 3D effects -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <!-- GSAP Animation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/ScrollTrigger.min.js"></script>

    <!-- Lottie Animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.10.0/lottie.min.js"></script>

    <!-- Styles -->
    <style>
        /* Base Styles */
        :root {
            --primary: #4e73df;
            --primary-dark: #224abe;
            --primary-light: #6f8df7;
            --secondary: #6c757d;
            --success: #1cc88a;
            --info: #36b9cc;
            --warning: #f6c23e;
            --danger: #e74a3b;
            --light: #f8f9fc;
            --dark: #5a5c69;
            --white: #fff;
            --gradient-primary: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            --gradient-success: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
            --gradient-info: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
            --gradient-warning: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
            --gradient-danger: linear-gradient(135deg, #e74a3b 0%, #be2617 100%);
            --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
            --shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.1);
            --shadow-primary: 0 5px 15px rgba(78, 115, 223, 0.2);
            --shadow-success: 0 5px 15px rgba(28, 200, 138, 0.2);
            --shadow-info: 0 5px 15px rgba(54, 185, 204, 0.2);
            --shadow-warning: 0 5px 15px rgba(246, 194, 62, 0.2);
            --shadow-danger: 0 5px 15px rgba(231, 74, 59, 0.2);
            --transition: all 0.3s ease;
            --border-radius: 0.5rem;
            --border-radius-lg: 1rem;
            --border-radius-sm: 0.25rem;
            --border-radius-xl: 1.5rem;
            --border-radius-2xl: 2rem;
            --border-radius-circle: 50%;
            --border-radius-pill: 50rem;
        }

        body {
            font-family: 'Poppins', sans-serif;
            overflow-x: hidden;
            background-color: #fff;
            color: #5a5c69;
            visibility: hidden;
        }

        body.loaded {
            visibility: visible;
        }

        .welcome-page {
            position: relative;
            overflow-x: hidden;
        }

        /* Preloader */
        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .preloader.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .preloader-content {
            text-align: center;
        }

        .preloader-spinner {
            width: 80px;
            height: 80px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        .typing-animation {
            overflow: hidden;
            border-right: 3px solid white;
            white-space: nowrap;
            margin: 0 auto;
            letter-spacing: 0.1em;
            animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
        }

        @keyframes typing {
            from { width: 0 }
            to { width: 100% }
        }

        @keyframes blink-caret {
            from, to { border-color: transparent }
            50% { border-color: white }
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Custom Cursor */
        .custom-cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            border: 2px solid var(--primary);
            border-radius: 50%;
            pointer-events: none;
            transform: translate(-50%, -50%);
            z-index: 9999;
            transition: width 0.3s, height 0.3s, border-color 0.3s;
            display: none;
        }

        /* Background Animation */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        #stars, #stars2, #stars3 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        #stars {
            background: transparent url('https://s3-us-west-2.amazonaws.com/s.cdpn.io/1231630/stars.png') repeat;
            animation: animateStars 50s linear infinite;
        }

        #stars2 {
            background: transparent url('https://s3-us-west-2.amazonaws.com/s.cdpn.io/1231630/stars2.png') repeat;
            animation: animateStars 100s linear infinite;
        }

        #stars3 {
            background: transparent url('https://s3-us-west-2.amazonaws.com/s.cdpn.io/1231630/stars3.png') repeat;
            animation: animateStars 150s linear infinite;
        }

        @keyframes animateStars {
            from { background-position: 0 0; }
            to { background-position: 0 1000px; }
        }

        /* Modern Navbar */
        .navbar-modern {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 20px 0;
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
        }

        .navbar-modern.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .brand-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .brand-icon-wrapper {
            position: relative;
            width: 55px;
            height: 55px;
        }

        .brand-icon {
            position: relative;
            width: 50px;
            height: 50px;
            background: var(--gradient-primary);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 8px 25px rgba(78, 115, 223, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 2;
        }

        .brand-icon-bg {
            position: absolute;
            top: 5px;
            left: 5px;
            width: 50px;
            height: 50px;
            background: rgba(78, 115, 223, 0.2);
            border-radius: 15px;
            z-index: 1;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .navbar-brand:hover .brand-icon {
            transform: translateY(-3px) rotate(5deg);
            box-shadow: 0 12px 35px rgba(78, 115, 223, 0.4);
        }

        .navbar-brand:hover .brand-icon-bg {
            transform: scale(1.1) rotate(-5deg);
        }

        .brand-text-container {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .brand-text {
            font-weight: 800;
            font-size: 1.6rem;
            line-height: 1;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            transition: var(--transition);
        }

        .brand-highlight {
            color: #fff;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .brand-subtitle {
            font-size: 0.7rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .navbar-modern.scrolled .brand-text {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .navbar-modern.scrolled .brand-highlight {
            color: #4e73df;
            text-shadow: none;
        }

        .navbar-modern.scrolled .brand-subtitle {
            color: rgba(78, 115, 223, 0.7);
        }

        /* Modern Nav Links */
        .nav-link-modern {
            position: relative;
            padding: 0;
            margin: 0 10px;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-link-content {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border-radius: 25px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-icon {
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .nav-text {
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .nav-indicator {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: var(--gradient-primary);
            border-radius: 2px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .nav-link-modern {
            color: rgba(255, 255, 255, 0.9);
        }

        .nav-link-modern:hover {
            color: white;
        }

        .nav-link-modern:hover .nav-link-content {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-link-modern:hover .nav-icon {
            transform: scale(1.1);
        }

        .nav-link-modern.active .nav-indicator {
            width: 30px;
        }

        .nav-link-modern.active {
            color: white;
        }

        .nav-link-modern.active .nav-link-content {
            background: rgba(255, 255, 255, 0.15);
        }

        /* Scrolled state */
        .navbar-modern.scrolled .nav-link-modern {
            color: rgba(78, 115, 223, 0.8);
        }

        .navbar-modern.scrolled .nav-link-modern:hover {
            color: var(--primary-color);
        }

        .navbar-modern.scrolled .nav-link-modern:hover .nav-link-content {
            background: rgba(78, 115, 223, 0.1);
        }

        .navbar-modern.scrolled .nav-link-modern.active {
            color: var(--primary-color);
        }

        .navbar-modern.scrolled .nav-link-modern.active .nav-link-content {
            background: rgba(78, 115, 223, 0.15);
        }

        /* Custom Toggler */
        .custom-toggler {
            border: none;
            background: none;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .custom-toggler:focus {
            box-shadow: none;
        }

        .toggler-line {
            display: block;
            width: 25px;
            height: 3px;
            background: white;
            margin: 5px 0;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .navbar-modern.scrolled .toggler-line {
            background: var(--primary-color);
        }

        .custom-toggler:hover .toggler-line {
            background: rgba(255, 255, 255, 0.8);
        }

        .navbar-modern.scrolled .custom-toggler:hover .toggler-line {
            background: rgba(78, 115, 223, 0.8);
        }

        /* Modern Buttons */
        .btn-modern {
            position: relative;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
            border: 2px solid transparent;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-modern:hover::before {
            left: 100%;
        }

        .btn-login {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 8px 25px rgba(78, 115, 223, 0.3);
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(78, 115, 223, 0.4);
            color: white;
        }

        .btn-register {
            background: transparent;
            color: white;
            border-color: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-register:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-3px);
            color: white;
        }

        .navbar-modern.scrolled .btn-register {
            color: var(--primary-color);
            border-color: rgba(78, 115, 223, 0.3);
        }

        .navbar-modern.scrolled .btn-register:hover {
            background: rgba(78, 115, 223, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .btn-user {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-color: rgba(255, 255, 255, 0.2);
        }

        .btn-user:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .navbar-modern.scrolled .btn-user {
            background: rgba(78, 115, 223, 0.1);
            color: var(--primary-color);
            border-color: rgba(78, 115, 223, 0.2);
        }

        .navbar-modern.scrolled .btn-user:hover {
            background: rgba(78, 115, 223, 0.2);
            color: var(--primary-color);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .user-name {
            font-weight: 600;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Modern Dropdown */
        .dropdown-modern {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            padding: 15px;
            margin-top: 15px;
            min-width: 220px;
        }

        .dropdown-modern .dropdown-item {
            border-radius: 12px;
            padding: 12px 16px;
            margin: 4px 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .dropdown-modern .dropdown-item:hover {
            background: rgba(78, 115, 223, 0.1);
            transform: translateX(5px);
        }

        .dropdown-modern .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        .dropdown-divider {
            margin: 10px 0;
            border-color: rgba(0, 0, 0, 0.1);
        }

        /* Button Animations */
        .btn-glow {
            position: relative;
            overflow: hidden;
            background: var(--gradient-primary);
            border: none;
            color: white;
            box-shadow: var(--shadow-primary);
            transition: var(--transition);
            z-index: 1;
        }

        .btn-glow:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(78, 115, 223, 0.3);
        }

        .btn-glow .btn-shine {
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(30deg);
            transition: var(--transition);
        }

        .btn-glow:hover .btn-shine {
            transform: rotate(30deg) translateY(-100px);
        }

        .btn-glow-sm {
            position: relative;
            overflow: hidden;
            transition: var(--transition);
        }

        .btn-glow-sm:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(78, 115, 223, 0.2);
        }

        .btn-hover-slide {
            position: relative;
            overflow: hidden;
            transition: var(--transition);
            z-index: 1;
        }

        .btn-hover-slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.1);
            transition: var(--transition);
            z-index: -1;
        }

        .btn-hover-slide:hover::before {
            width: 100%;
        }

        .btn-hover-slide:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 255, 255, 0.1);
        }

        .pulse-btn {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(78, 115, 223, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(78, 115, 223, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(78, 115, 223, 0);
            }
        }

        /* Hero Section */
        .hero-section {
            position: relative;
            min-height: 100vh;
            background: var(--gradient-primary);
            overflow: hidden;
            padding-top: 80px;
        }

        .hero-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }



        .hero-content {
            position: relative;
            z-index: 3;
            color: white;
        }

        .hero-title {
            font-size: 3rem;
            line-height: 1.2;
            margin-bottom: 1.5rem;
        }

        .text-gradient {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .text-white-80 {
            color: rgba(255, 255, 255, 0.8);
        }

        .hero-wave {
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            z-index: 4;
        }

        .hero-stats {
            margin-top: 2rem;
        }

        .stat-item {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            border-radius: var(--border-radius);
            padding: 1rem;
            text-align: center;
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background-color: rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .login-card-container {
            position: relative;
            z-index: 3;
            perspective: 1000px;
        }

        .login-card {
            background-color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            transform-style: preserve-3d;
            transform: rotateY(0deg);
            position: relative;
            overflow: hidden;
        }

        .login-card:hover {
            transform: rotateY(5deg);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }

        .login-card-decoration {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(78, 115, 223, 0.1) 0%, rgba(78, 115, 223, 0.3) 100%);
        }

        .decoration-circle-1 {
            top: -50px;
            right: -50px;
            width: 200px;
            height: 200px;
        }

        .decoration-circle-2 {
            bottom: -30px;
            left: -30px;
            width: 150px;
            height: 150px;
        }

        .decoration-circle-3 {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            opacity: 0.05;
        }

        .app-logo {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            color: white;
            font-size: 2rem;
            box-shadow: var(--shadow-primary);
        }

        /* Login Features */
        .login-features {
            margin-top: 1.5rem;
        }

        .login-feature-title {
            position: relative;
            text-align: center;
            margin-bottom: 1rem;
        }

        .login-feature-title::before,
        .login-feature-title::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 30%;
            height: 1px;
            background-color: #e9ecef;
        }

        .login-feature-title::before {
            left: 0;
        }

        .login-feature-title::after {
            right: 0;
        }

        .login-feature-title span {
            display: inline-block;
            padding: 0 10px;
            background-color: white;
            position: relative;
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .login-feature-list {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .login-feature-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 0.5rem;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }

        .login-feature-item:hover {
            background-color: #e9ecef;
            transform: translateX(5px);
        }

        .login-feature-item i {
            font-size: 1rem;
            color: #1cc88a;
        }

        .login-feature-item span {
            font-size: 0.9rem;
            font-weight: 500;
            color: #5a5c69;
        }

        .btn-with-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        /* Features Section */
        .features-section {
            position: relative;
            padding: 5rem 0;
            background-color: #fff;
            overflow: hidden;
        }

        .section-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            pointer-events: none;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            opacity: 0.05;
        }

        .shape-1 {
            top: 10%;
            left: 5%;
            width: 300px;
            height: 300px;
            background-color: var(--primary);
        }

        .shape-2 {
            bottom: 10%;
            right: 5%;
            width: 250px;
            height: 250px;
            background-color: var(--success);
        }

        .shape-3 {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 500px;
            height: 500px;
            background-color: var(--info);
            opacity: 0.03;
        }

        .shape-4 {
            bottom: 30%;
            left: 10%;
            width: 200px;
            height: 200px;
            background-color: var(--warning);
        }

        .section-header {
            position: relative;
            z-index: 1;
            margin-bottom: 3rem;
        }

        .section-subtitle {
            display: inline-block;
            padding: 0.5rem 1rem;
            background-color: rgba(78, 115, 223, 0.1);
            color: var(--primary);
            border-radius: var(--border-radius-pill);
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .section-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .section-divider {
            width: 80px;
            height: 4px;
            background: var(--gradient-primary);
            margin: 1.5rem auto;
            border-radius: var(--border-radius-pill);
        }

        .section-description {
            max-width: 700px;
            margin: 0 auto;
            color: #6c757d;
        }

        .feature-card {
            position: relative;
            background-color: white;
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
            border: 1px solid #f1f1f1;
            height: 100%;
            z-index: 1;
            overflow: hidden;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-lg);
        }

        .feature-card-highlighted {
            border-color: rgba(78, 115, 223, 0.3);
            box-shadow: var(--shadow-primary);
        }

        .feature-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: var(--gradient-primary);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: var(--border-radius-pill);
            font-size: 0.75rem;
            font-weight: 600;
            box-shadow: var(--shadow-primary);
            overflow: hidden;
        }

        .badge-shine {
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(30deg);
            animation: shine 3s linear infinite;
        }

        @keyframes shine {
            0% { transform: rotate(30deg) translateY(0); }
            100% { transform: rotate(30deg) translateY(200px); }
        }

        .feature-icon-wrapper {
            position: relative;
            width: 70px;
            height: 70px;
            margin-bottom: 1.5rem;
        }

        .feature-icon {
            position: relative;
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: var(--shadow-primary);
            z-index: 2;
            transition: var(--transition);
        }

        .feature-icon-bg {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 60px;
            height: 60px;
            background-color: rgba(78, 115, 223, 0.2);
            border-radius: var(--border-radius);
            z-index: 1;
            transition: var(--transition);
        }

        .feature-card:hover .feature-icon {
            transform: translateY(-5px);
        }

        .feature-card:hover .feature-icon-bg {
            transform: scale(1.2) rotate(10deg);
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #5a5c69;
        }

        .feature-description {
            color: #6c757d;
            margin-bottom: 1.5rem;
        }

        .feature-hover {
            opacity: 0;
            transform: translateY(20px);
            transition: var(--transition);
        }

        .feature-card:hover .feature-hover {
            opacity: 1;
            transform: translateY(0);
        }

        .feature-shape {
            position: absolute;
            bottom: -50px;
            right: -50px;
            width: 150px;
            height: 150px;
            background-color: rgba(78, 115, 223, 0.05);
            border-radius: 50%;
            z-index: -1;
            transition: var(--transition);
        }

        .feature-card:hover .feature-shape {
            transform: scale(1.2);
        }
    </style>
</head>
<body class="welcome-page">
    <!-- Preloader -->
    <div class="preloader">
        <div class="preloader-content">
            <div class="preloader-spinner"></div>
            <div class="preloader-text mt-3 text-white">
                <div class="typing-animation">Loading Absensi App...</div>
            </div>
        </div>
    </div>

    <!-- Cursor Animation -->
    <div class="custom-cursor"></div>

    <!-- Background Animation -->
    <div class="bg-animation">
        <div id="stars"></div>
        <div id="stars2"></div>
        <div id="stars3"></div>
    </div>

    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top navbar-modern">
        <div class="container-fluid px-4">
            <a class="navbar-brand fw-bold" href="#" data-aos="fade-right">
                <div class="brand-container">
                    <div class="brand-icon-wrapper">
                        <div class="brand-icon">
                            <i class="bi bi-calendar-check"></i>
                        </div>
                        <div class="brand-icon-bg"></div>
                    </div>
                    <div class="brand-text-container">
                        <div class="brand-text">Absensi<span class="brand-highlight">App</span></div>
                        <div class="brand-subtitle">Sistem Absensi Modern</div>
                    </div>
                </div>
            </a>

            <button class="navbar-toggler custom-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="toggler-line"></span>
                <span class="toggler-line"></span>
                <span class="toggler-line"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto" data-aos="fade-down" data-aos-delay="200">
                    <li class="nav-item">
                        <a class="nav-link nav-link-modern active" href="#home">
                            <div class="nav-link-content">
                                <i class="bi bi-house-door nav-icon"></i>
                                <span class="nav-text">Beranda</span>
                                <div class="nav-indicator"></div>
                            </div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-modern" href="#features">
                            <div class="nav-link-content">
                                <i class="bi bi-grid-3x3-gap nav-icon"></i>
                                <span class="nav-text">Fitur</span>
                                <div class="nav-indicator"></div>
                            </div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-modern" href="#contact">
                            <div class="nav-link-content">
                                <i class="bi bi-envelope nav-icon"></i>
                                <span class="nav-text">Kontak</span>
                                <div class="nav-indicator"></div>
                            </div>
                        </a>
                    </li>
                </ul>

                <div class="navbar-actions d-flex align-items-center gap-3" data-aos="fade-left" data-aos-delay="400">
                    @guest
                        <a href="{{ route('login') }}" class="btn btn-modern btn-login">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            <span>Login</span>
                        </a>
                        <a href="{{ route('register') }}" class="btn btn-modern btn-register">
                            <i class="bi bi-person-plus me-2"></i>
                            <span>Register</span>
                        </a>
                    @endguest

                    @auth
                        <div class="dropdown user-dropdown">
                            <button class="btn btn-modern btn-user dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                                <div class="user-avatar">
                                    <i class="bi bi-person-circle"></i>
                                </div>
                                <span class="user-name">{{ Auth::user()->name }}</span>
                            </button>
                            <ul class="dropdown-menu dropdown-modern" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="{{ route('profile.show') }}"><i class="bi bi-person me-2"></i>Profil Saya</a></li>
                                <li><a class="dropdown-item" href="{{ route('profile.edit') }}"><i class="bi bi-pencil-square me-2"></i>Edit Profil</a></li>
                                <li><a class="dropdown-item" href="{{ route('profile.password.edit') }}"><i class="bi bi-key me-2"></i>Ganti Password</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button class="dropdown-item text-danger">
                                            <i class="bi bi-box-arrow-right me-2"></i>Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    @endauth
                </div>
            </div>
        </div>
    </nav>
<div class="welcome-page">
    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="hero-particles" id="particles-js"></div>


        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6 mb-5 mb-lg-0">
                    <div class="hero-content text-center text-lg-start" data-aos="fade-right" data-aos-duration="1000">
                        <div class="badge-container mb-3">
                            <span class="badge bg-white text-primary px-3 py-2 rounded-pill animate__animated animate__fadeInDown animate__delay-1s">
                                <i class="bi bi-stars me-1"></i> Sistem Absensi Terbaik
                            </span>
                        </div>
                        <h1 class="hero-title fw-bold mb-3 animate__animated animate__fadeInUp animate__delay-1s">
                            <span class="text-gradient">Sistem Absensi</span> Modern untuk <br>
                            <span class="typewriter-text"></span>
                        </h1>
                        <p class="lead mb-4 text-white-80 animate__animated animate__fadeInUp animate__delay-1s">
                            Kelola kehadiran karyawan dengan mudah, cepat, dan akurat. Dilengkapi dengan fitur absensi foto dan tanda tangan digital untuk pengalaman yang lebih baik.
                        </p>
                        <div class="d-flex flex-wrap gap-3 justify-content-center justify-content-lg-start animate__animated animate__fadeInUp animate__delay-2s">
                            <a href="{{ route('login') }}" class="btn btn-glow btn-lg pulse-btn">
                                <span class="btn-text"><i class="bi bi-box-arrow-in-right me-2"></i>Masuk Sekarang</span>
                                <span class="btn-shine"></span>
                            </a>
                            <a href="{{ route('register') }}" class="btn btn-outline-light btn-lg btn-hover-slide">
                                <i class="bi bi-person-plus me-2"></i>Daftar
                            </a>
                        </div>
                        <div class="hero-stats mt-5 animate__animated animate__fadeInUp animate__delay-2s">
                            <div class="row g-3">
                                <div class="col-4">
                                    <div class="stat-item" data-aos="zoom-in" data-aos-delay="200">
                                        <div class="stat-number counter" data-target="100">0</div>
                                        <div class="stat-label">Akurat</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item" data-aos="zoom-in" data-aos-delay="400">
                                        <div class="stat-number">24/7</div>
                                        <div class="stat-label">Tersedia</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item" data-aos="zoom-in" data-aos-delay="600">
                                        <div class="stat-number counter" data-target="100">0</div>
                                        <div class="stat-label">Pengguna</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="300">
                    <div class="login-card-container">
                        <div class="login-card">
                            <div class="login-card-header text-center">
                                <div class="app-logo mb-4 animate__animated animate__bounceIn animate__delay-2s">
                                    <i class="bi bi-calendar-check"></i>
                                </div>
                                <h2 class="fw-bold text-primary animate__animated animate__fadeInUp animate__delay-2s">Sistem Absensi</h2>
                                <p class="text-muted animate__animated animate__fadeInUp animate__delay-2s">Masuk untuk mengakses sistem</p>
                            </div>
                            <div class="login-card-body">
                                <div class="d-grid gap-3">
                                    <a href="{{ route('login') }}" class="btn btn-primary btn-lg btn-with-icon animate__animated animate__fadeInUp animate__delay-2s">
                                        <i class="bi bi-box-arrow-in-right me-2"></i> Login
                                    </a>
                                    <a href="{{ route('register') }}" class="btn btn-outline-secondary btn-lg btn-with-icon animate__animated animate__fadeInUp animate__delay-2s">
                                        <i class="bi bi-person-plus me-2"></i> Register
                                    </a>
                                </div>
                            </div>
                            <div class="login-card-decoration">
                                <div class="decoration-circle decoration-circle-1"></div>
                                <div class="decoration-circle decoration-circle-2"></div>
                                <div class="decoration-circle decoration-circle-3"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="hero-wave">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
                <path fill="#ffffff" fill-opacity="1" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
            </svg>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section py-6">
        <div class="section-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
        </div>
        <div class="container">
            <div class="section-header text-center mb-5" data-aos="fade-up">
                <span class="section-subtitle animate__animated animate__fadeInUp">Fitur Unggulan</span>
                <h2 class="section-title fw-bold animate__animated animate__fadeInUp">
                    Fitur <span class="text-primary">Canggih</span> untuk Kebutuhan Perusahaan Modern
                </h2>
                <div class="section-divider mx-auto"></div>
                <p class="section-description animate__animated animate__fadeInUp">
                    Sistem absensi modern dengan berbagai fitur canggih yang dirancang untuk memudahkan pengelolaan kehadiran karyawan
                </p>
            </div>

            <div class="row g-4 justify-content-center">
                <div class="col-md-4 col-lg-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-card hover-lift">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon">
                                <i class="bi bi-camera"></i>
                            </div>
                            <div class="feature-icon-bg"></div>
                        </div>
                        <h3 class="feature-title">Absensi dengan Foto</h3>
                        <p class="feature-description">Verifikasi kehadiran dengan foto untuk memastikan keakuratan data absensi dan mencegah kecurangan.</p>
                        <div class="feature-hover">
                            <a href="{{ route('login') }}" class="btn btn-sm btn-primary rounded-pill btn-glow-sm">
                                <span>Coba Sekarang</span>
                                <i class="bi bi-arrow-right ms-1"></i>
                            </a>
                        </div>
                        <div class="feature-shape"></div>
                    </div>
                </div>

                <div class="col-md-4 col-lg-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card hover-lift feature-card-highlighted">
                        <div class="feature-badge">
                            <span>Populer</span>
                            <div class="badge-shine"></div>
                        </div>
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon">
                                <i class="bi bi-pen"></i>
                            </div>
                            <div class="feature-icon-bg"></div>
                        </div>
                        <h3 class="feature-title">Tanda Tangan Digital</h3>
                        <p class="feature-description">Lengkapi absensi dengan tanda tangan digital untuk verifikasi tambahan dan keamanan data.</p>
                        <div class="feature-hover">
                            <a href="{{ route('login') }}" class="btn btn-sm btn-primary rounded-pill btn-glow-sm">
                                <span>Coba Sekarang</span>
                                <i class="bi bi-arrow-right ms-1"></i>
                            </a>
                        </div>
                        <div class="feature-shape"></div>
                    </div>
                </div>

                <div class="col-md-4 col-lg-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card hover-lift">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <div class="feature-icon-bg"></div>
                        </div>
                        <h3 class="feature-title">Laporan Lengkap</h3>
                        <p class="feature-description">Dapatkan laporan kehadiran lengkap dengan berbagai filter dan format ekspor PDF yang mudah digunakan.</p>
                        <div class="feature-hover">
                            <a href="{{ route('login') }}" class="btn btn-sm btn-primary rounded-pill btn-glow-sm">
                                <span>Coba Sekarang</span>
                                <i class="bi bi-arrow-right ms-1"></i>
                            </a>
                        </div>
                        <div class="feature-shape"></div>
                    </div>
                </div>

                <div class="col-md-4 col-lg-4" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-card hover-lift">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon">
                                <i class="bi bi-calendar-check"></i>
                            </div>
                            <div class="feature-icon-bg"></div>
                        </div>
                        <h3 class="feature-title">Manajemen Izin & Cuti</h3>
                        <p class="feature-description">Kelola pengajuan izin, cuti, dan sakit dengan sistem persetujuan yang terstruktur dan transparan.</p>
                        <div class="feature-hover">
                            <a href="{{ route('login') }}" class="btn btn-sm btn-primary rounded-pill btn-glow-sm">
                                <span>Coba Sekarang</span>
                                <i class="bi bi-arrow-right ms-1"></i>
                            </a>
                        </div>
                        <div class="feature-shape"></div>
                    </div>
                </div>

                <div class="col-md-4 col-lg-4" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-card hover-lift">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon">
                                <i class="bi bi-bell"></i>
                            </div>
                            <div class="feature-icon-bg"></div>
                        </div>
                        <h3 class="feature-title">Notifikasi Realtime</h3>
                        <p class="feature-description">Dapatkan notifikasi untuk setiap aktivitas absensi dan persetujuan secara langsung dan tepat waktu.</p>
                        <div class="feature-hover">
                            <a href="{{ route('login') }}" class="btn btn-sm btn-primary rounded-pill btn-glow-sm">
                                <span>Coba Sekarang</span>
                                <i class="bi bi-arrow-right ms-1"></i>
                            </a>
                        </div>
                        <div class="feature-shape"></div>
                    </div>
                </div>

                <div class="col-md-4 col-lg-4" data-aos="fade-up" data-aos-delay="600">
                    <div class="feature-card hover-lift">
                        <div class="feature-icon-wrapper">
                            <div class="feature-icon">
                                <i class="bi bi-shield-check"></i>
                            </div>
                            <div class="feature-icon-bg"></div>
                        </div>
                        <h3 class="feature-title">Keamanan Tinggi</h3>
                        <p class="feature-description">Data absensi dilindungi dengan sistem keamanan berlapis untuk menjaga kerahasiaan informasi perusahaan.</p>
                        <div class="feature-hover">
                            <a href="{{ route('login') }}" class="btn btn-sm btn-primary rounded-pill btn-glow-sm">
                                <span>Coba Sekarang</span>
                                <i class="bi bi-arrow-right ms-1"></i>
                            </a>
                        </div>
                        <div class="feature-shape"></div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="700">
                <a href="{{ route('login') }}" class="btn btn-lg btn-primary rounded-pill btn-glow">
                    <span class="btn-text">
                        <i class="bi bi-arrow-right-circle me-2"></i> Mulai Gunakan Sekarang
                    </span>
                    <span class="btn-shine"></span>
                </a>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <div class="how-it-works-section py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">Cara Kerja</h2>
                <p class="text-muted">Proses absensi yang mudah dan cepat</p>
            </div>

            <div class="row">
                <div class="col-lg-10 mx-auto">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-dot">1</div>
                            <div class="timeline-content">
                                <h4>Login ke Aplikasi</h4>
                                <p>Masuk ke aplikasi menggunakan akun yang telah terdaftar.</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot">2</div>
                            <div class="timeline-content">
                                <h4>Absen Masuk</h4>
                                <p>Lakukan absen masuk dengan foto untuk verifikasi kehadiran.</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot">3</div>
                            <div class="timeline-content">
                                <h4>Aktivitas Kerja</h4>
                                <p>Lakukan aktivitas kerja seperti biasa.</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot">4</div>
                            <div class="timeline-content">
                                <h4>Absen Keluar</h4>
                                <p>Lakukan absen keluar setelah selesai bekerja.</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot">5</div>
                            <div class="timeline-content">
                                <h4>Lihat Laporan</h4>
                                <p>Lihat laporan absensi dan unduh dalam format PDF.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Section -->
    <section id="contact" class="contact-section py-5">
        <div class="container">
            <div class="section-header text-center mb-5" data-aos="fade-up">
                <span class="section-subtitle animate__animated animate__fadeInUp">Hubungi Kami</span>
                <h2 class="section-title fw-bold animate__animated animate__fadeInUp">
                    Punya <span class="text-primary">Pertanyaan</span>? Hubungi Kami
                </h2>
                <div class="section-divider mx-auto"></div>
                <p class="section-description animate__animated animate__fadeInUp">
                    Kami siap membantu Anda dengan segala pertanyaan tentang sistem absensi kami
                </p>
            </div>

            <div class="row g-4">
                <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="bi bi-geo-alt"></i>
                        </div>
                        <h4>Alamat</h4>
                        <p>Jl. Kolonel Masturi. 138, Cimahi Utara</p>
                    </div>
                </div>

                <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="bi bi-whatsapp"></i>
                        </div>
                        <h4>WhatsApp</h4>
                        <p><a href="https://wa.me/6289525311228?text=Halo,%20saya%20tertarik%20dengan%20Aplikasi%20Absensi.%20Bisa%20minta%20informasi%20lebih%20lanjut?" target="_blank" class="text-decoration-none text-success hover-text-dark">089525311228</a></p>
                    </div>
                </div>

                <div class="col-md-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="bi bi-envelope"></i>
                        </div>
                        <h4>Email</h4>
                        <p><a href="mailto:<EMAIL>" class="text-decoration-none text-primary hover-text-dark"><EMAIL></a></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <div class="cta-section py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mb-4 mb-lg-0" data-aos="fade-right">
                    <h2 class="fw-bold text-white animate__animated animate__fadeInUp">Siap untuk meningkatkan efisiensi absensi karyawan Anda?</h2>
                    <p class="lead text-white-80 mb-0 animate__animated animate__fadeInUp animate__delay-1s">Daftar sekarang dan nikmati kemudahan mengelola absensi karyawan.</p>
                </div>
                <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                    <a href="{{ route('register') }}" class="btn btn-light btn-lg pulse-btn animate__animated animate__fadeInUp animate__delay-1s">
                        <i class="bi bi-rocket me-2"></i> Mulai Sekarang
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer py-4">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <h5>Tentang Kami</h5>
                    <p>Aplikasi Absensi adalah solusi modern untuk mengelola kehadiran karyawan dengan mudah, cepat, dan akurat.</p>
                </div>
                <div class="col-md-4">
                    <h5>Kontak</h5>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-geo-alt me-2"></i> Jalan Kolonel Masturi No 138, Cimahi Utara</li>
                        <li><i class="bi bi-whatsapp me-2"></i> <a href="https://wa.me/6289525311228?text=Halo,%20saya%20tertarik%20dengan%20Aplikasi%20Absensi.%20Bisa%20minta%20informasi%20lebih%20lanjut?" target="_blank" class="text-decoration-none text-success hover-text-dark">089525311228</a></li>
                        <li><i class="bi bi-envelope me-2"></i> <a href="mailto:<EMAIL>" class="text-decoration-none text-primary hover-text-dark"><EMAIL></a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Tautan</h5>
                    <ul class="list-unstyled">
                        <li><a href="#home" class="footer-link">Beranda</a></li>
                        <li><a href="#features" class="footer-link">Fitur</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; {{ date('Y') }} Aplikasi Absensi. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Floating WhatsApp Button -->
    <div class="whatsapp-float">
        <a href="https://wa.me/6289525311228?text=Halo,%20saya%20tertarik%20dengan%20Aplikasi%20Absensi.%20Bisa%20minta%20informasi%20lebih%20lanjut?" target="_blank" class="whatsapp-btn">
            <i class="bi bi-whatsapp"></i>
        </a>
    </div>
</div>

<!-- Script untuk smooth scrolling -->
<script>
    $(document).ready(function() {
        // Smooth scrolling untuk semua link dengan hash (#)
        $('a[href^="#"]').on('click', function(event) {
            var target = $(this.getAttribute('href'));
            if(target.length) {
                event.preventDefault();
                $('html, body').stop().animate({
                    scrollTop: target.offset().top - 80 // Offset untuk navbar
                }, 800);
            }
        });

        // Aktifkan link navbar sesuai dengan section yang sedang dilihat
        $(window).scroll(function() {
            var scrollPos = $(document).scrollTop();

            // Ubah navbar saat scroll
            if (scrollPos > 50) {
                $('.navbar-modern').addClass('scrolled');
            } else {
                $('.navbar-modern').removeClass('scrolled');
            }

            // Aktifkan link navbar sesuai dengan section yang sedang dilihat
            $('.nav-link-modern').each(function() {
                var currLink = $(this);
                var refElement = $(currLink.attr("href"));
                if (refElement.length && refElement.position().top - 100 <= scrollPos && refElement.position().top + refElement.height() > scrollPos) {
                    $('.nav-link-modern').removeClass("active");
                    currLink.addClass("active");
                } else {
                    currLink.removeClass("active");
                }
            });
        });

        // Tambahkan animasi hover pada tombol navbar
        $('.btn-modern').hover(
            function() {
                $(this).addClass('animate__animated animate__pulse');
            },
            function() {
                $(this).removeClass('animate__animated animate__pulse');
            }
        );

        // Animasi untuk brand icon
        $('.brand-icon').hover(
            function() {
                $(this).addClass('animate__animated animate__rubberBand');
            },
            function() {
                $(this).removeClass('animate__animated animate__rubberBand');
            }
        );
    });
</script>

<style>
/* Global Styles */
.welcome-page {
    --primary-color: #4e73df;
    --primary-dark: #2e59d9;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
    --gradient-primary: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    --gradient-secondary: linear-gradient(135deg, #858796 0%, #60616f 100%);
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --transition: all 0.3s ease;
    color: #333;
}

/* Email link hover effect */
.hover-text-dark:hover {
    color: var(--primary-dark) !important;
    text-decoration: underline !important;
    transition: all 0.3s ease;
}

/* Email link styling */
.text-primary {
    color: #4e73df !important;
}

.text-primary:hover {
    color: #2e59d9 !important;
    text-decoration: underline !important;
    transition: all 0.3s ease;
}

/* Email icon animation */
.bi-envelope {
    transition: all 0.3s ease;
}

.bi-envelope:hover {
    transform: scale(1.1);
    color: #4e73df;
}

/* WhatsApp link styling */
.text-success {
    color: #25d366 !important;
}

.text-success:hover {
    color: #128c7e !important;
    text-decoration: underline !important;
    transition: all 0.3s ease;
}

/* WhatsApp icon animation */
.bi-whatsapp {
    transition: all 0.3s ease;
}

.bi-whatsapp:hover {
    transform: scale(1.2);
    color: #25d366;
}

/* Floating WhatsApp Button */
.whatsapp-float {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.whatsapp-btn {
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    text-decoration: none;
    box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    transition: all 0.3s ease;
    animation: whatsapp-pulse 2s infinite;
}

.whatsapp-btn:hover {
    background: #128c7e;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(37, 211, 102, 0.6);
}

@keyframes whatsapp-pulse {
    0% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4), 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4), 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4), 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* Responsive WhatsApp Button */
@media (max-width: 768px) {
    .whatsapp-float {
        bottom: 20px;
        right: 20px;
    }

    .whatsapp-btn {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
}

/* Footer links */
.footer-link {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    padding-left: 0;
}

.footer-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
    padding-left: 5px;
}

.footer-link::before {
    content: "→";
    position: absolute;
    left: -20px;
    opacity: 0;
    transition: all 0.3s ease;
}

.footer-link:hover::before {
    left: -15px;
    opacity: 1;
}

/* Contact Section */
.contact-section {
    background-color: #f8f9fc;
    position: relative;
    overflow: hidden;
}

.contact-card {
    background-color: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid #f1f1f1;
}

.contact-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border-color: rgba(78, 115, 223, 0.3);
}

.contact-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 1.8rem;
    box-shadow: 0 5px 15px rgba(78, 115, 223, 0.3);
}

.contact-card h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #5a5c69;
}

.contact-card p {
    color: #6c757d;
    margin-bottom: 0;
}

/* CTA Section */
.cta-section {
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
    padding: 60px 0;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1497215842964-222b430dc094');
    background-size: cover;
    background-position: center;
    opacity: 0.1;
    z-index: 0;
}

.cta-section .container {
    position: relative;
    z-index: 1;
}

.text-white-80 {
    color: rgba(255, 255, 255, 0.8);
}

/* Text Gradient */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    color: white;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1497215842964-222b430dc094');
    background-size: cover;
    background-position: center;
    opacity: 0.1;
    z-index: 0;
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

.min-vh-100 {
    min-height: 100vh;
}

/* Login Card */
.login-card-container {
    perspective: 1000px;
}

.login-card {
    background: white;
    border-radius: 15px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transform-style: preserve-3d;
    transition: var(--transition);
    transform: rotateY(0deg);
}

.login-card:hover {
    transform: rotateY(5deg);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.login-card-header {
    padding: 30px 30px 20px;
    background: var(--light-color);
}

.login-card-body {
    padding: 30px 30px 40px;
}

.app-logo {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 2.5rem;
    color: white;
}

/* Pulse Button */
.pulse-btn {
    position: relative;
    box-shadow: 0 0 0 0 rgba(78, 115, 223, 0.7);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(78, 115, 223, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(78, 115, 223, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(78, 115, 223, 0);
    }
}

/* Features Section */
.features-section {
    background-color: var(--light-color);
    position: relative;
}

.feature-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
    z-index: -1;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
}

/* How It Works Section */
.how-it-works-section {
    background-color: white;
    position: relative;
}

.timeline {
    position: relative;
    padding: 30px 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    margin-bottom: 50px;
    display: flex;
    align-items: center;
}

.timeline-dot {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
    z-index: 2;
    box-shadow: var(--shadow);
}

.timeline-content {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-left: 20px;
    box-shadow: var(--shadow);
    flex: 1;
}

/* CTA Section */
.cta-section {
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1454165804606-c3d57bc86b40');
    background-size: cover;
    background-position: center;
    opacity: 0.1;
    z-index: 0;
}

.cta-section .container {
    position: relative;
    z-index: 1;
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: white;
}

.footer-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
}

.footer-link:hover {
    color: white;
    text-decoration: underline;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .timeline::before {
        left: 30px;
    }

    .timeline-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .timeline-content {
        margin-left: 0;
        margin-top: 20px;
        width: 100%;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Preloader
    setTimeout(function() {
        const preloader = document.querySelector('.preloader');
        preloader.classList.add('hidden');
        document.body.classList.add('loaded');
    }, 1500);

    // Initialize AOS
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: false,
            mirror: true
        });
    }

    // Initialize Particles.js
    if (typeof particlesJS !== 'undefined') {
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 80,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": "#ffffff"
                },
                "shape": {
                    "type": "circle",
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    },
                    "polygon": {
                        "nb_sides": 5
                    }
                },
                "opacity": {
                    "value": 0.5,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 3,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 2,
                        "size_min": 0.1,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#ffffff",
                    "opacity": 0.4,
                    "width": 1
                },
                "move": {
                    "enable": true,
                    "speed": 2,
                    "direction": "none",
                    "random": true,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": true,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "grab"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "grab": {
                        "distance": 140,
                        "line_linked": {
                            "opacity": 1
                        }
                    },
                    "bubble": {
                        "distance": 400,
                        "size": 40,
                        "duration": 2,
                        "opacity": 8,
                        "speed": 3
                    },
                    "repulse": {
                        "distance": 200,
                        "duration": 0.4
                    },
                    "push": {
                        "particles_nb": 4
                    },
                    "remove": {
                        "particles_nb": 2
                    }
                }
            },
            "retina_detect": true
        });
    }

    // Custom cursor
    const cursor = document.querySelector('.custom-cursor');
    if (cursor && window.innerWidth > 992) {
        cursor.style.display = 'block';

        document.addEventListener('mousemove', function(e) {
            cursor.style.left = e.clientX + 'px';
            cursor.style.top = e.clientY + 'px';
        });

        document.addEventListener('mousedown', function() {
            cursor.style.width = '15px';
            cursor.style.height = '15px';
            cursor.style.borderColor = 'var(--primary-light)';
        });

        document.addEventListener('mouseup', function() {
            cursor.style.width = '20px';
            cursor.style.height = '20px';
            cursor.style.borderColor = 'var(--primary)';
        });

        // Change cursor on links and buttons
        const links = document.querySelectorAll('a, button');
        links.forEach(link => {
            link.addEventListener('mouseenter', function() {
                cursor.style.width = '30px';
                cursor.style.height = '30px';
                cursor.style.borderColor = 'var(--primary-light)';
                cursor.style.borderWidth = '1px';
            });

            link.addEventListener('mouseleave', function() {
                cursor.style.width = '20px';
                cursor.style.height = '20px';
                cursor.style.borderColor = 'var(--primary)';
                cursor.style.borderWidth = '2px';
            });
        });
    }

    // Navbar scroll effect
    const navbar = document.querySelector('.navbar-glass');
    if (navbar) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    }

    // Counter animation
    const counters = document.querySelectorAll('.counter');
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000; // 2 seconds
        const step = target / (duration / 16); // 60fps

        let current = 0;
        const updateCounter = () => {
            current += step;
            if (current < target) {
                counter.textContent = Math.ceil(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
                if (target === 100) {
                    counter.textContent += '%';
                }
                if (target === 100 && counter.nextElementSibling && counter.nextElementSibling.textContent === 'Pengguna') {
                    counter.textContent = '100+';
                }
            }
        };

        // Start counter animation when element is in viewport
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateCounter();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        observer.observe(counter);
    });

    // Typewriter effect
    const typewriterElement = document.querySelector('.typewriter-text');
    if (typewriterElement) {
        const words = ['Perusahaan Anda', 'Bisnis Anda', 'Organisasi Anda', 'Tim Anda'];
        let wordIndex = 0;
        let charIndex = 0;
        let isDeleting = false;
        let typeSpeed = 100;

        function type() {
            const currentWord = words[wordIndex];

            if (isDeleting) {
                typewriterElement.textContent = currentWord.substring(0, charIndex - 1);
                charIndex--;
                typeSpeed = 50;
            } else {
                typewriterElement.textContent = currentWord.substring(0, charIndex + 1);
                charIndex++;
                typeSpeed = 100;
            }

            if (!isDeleting && charIndex === currentWord.length) {
                isDeleting = true;
                typeSpeed = 1000; // Pause at end of word
            } else if (isDeleting && charIndex === 0) {
                isDeleting = false;
                wordIndex = (wordIndex + 1) % words.length;
                typeSpeed = 500; // Pause before starting new word
            }

            setTimeout(type, typeSpeed);
        }

        // Start typewriter after a delay
        setTimeout(type, 2000);
    }

    // 3D tilt effect
    const cards = document.querySelectorAll('.login-card, .feature-card');
    cards.forEach(card => {
        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / 20;
            const rotateY = (centerX - x) / 20;

            this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0)';
        });
    });

    // Animate elements when they come into view
    const animateOnScroll = function() {
        const elements = document.querySelectorAll('.feature-card, .timeline-item, .login-card');

        elements.forEach(element => {
            const elementPosition = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;

            if (elementPosition < windowHeight - 100) {
                element.style.opacity = '1';
                element.style.transform = element.classList.contains('feature-card')
                    ? 'translateY(0)'
                    : element.classList.contains('login-card')
                    ? 'rotateY(0deg)'
                    : 'translateX(0)';
            }
        });
    };

    // Set initial state for animations
    const elementsToAnimate = document.querySelectorAll('.feature-card, .timeline-item');
    elementsToAnimate.forEach(element => {
        element.style.opacity = '0';
        element.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

        if (element.classList.contains('feature-card')) {
            element.style.transform = 'translateY(50px)';
        } else {
            element.style.transform = 'translateX(50px)';
        }
    });

    // Run animation on load and scroll
    animateOnScroll();
    window.addEventListener('scroll', animateOnScroll);
});
</script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>
</body>
</html>
