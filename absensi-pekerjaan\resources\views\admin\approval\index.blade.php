@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-lg border-0 rounded-lg">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Persetujuan Pengajuan Izin/Cuti/Sakit</h5>
                </div>
                <div class="card-body">
                    <!-- Filter Form -->
                    <form action="{{ route('admin.approval.index') }}" method="GET" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="tanggal_mulai" class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" id="tanggal_mulai" name="tanggal_mulai" value="{{ request('tanggal_mulai') }}">
                            </div>
                            <div class="col-md-3">
                                <label for="tanggal_akhir" class="form-label">Tanggal Akhir</label>
                                <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="{{ request('tanggal_akhir') }}">
                            </div>
                            <div class="col-md-2">
                                <label for="user_id" class="form-label">Karyawan</label>
                                <select class="form-select" id="user_id" name="user_id">
                                    <option value="">Semua Karyawan</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="jenis" class="form-label">Jenis</label>
                                <select class="form-select" id="jenis" name="jenis">
                                    <option value="">Semua Jenis</option>
                                    <option value="izin" {{ request('jenis') == 'izin' ? 'selected' : '' }}>Izin</option>
                                    <option value="sakit" {{ request('jenis') == 'sakit' ? 'selected' : '' }}>Sakit</option>
                                    <option value="cuti" {{ request('jenis') == 'cuti' ? 'selected' : '' }}>Cuti</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="status_approval" class="form-label">Status</label>
                                <select class="form-select" id="status_approval" name="status_approval">
                                    <option value="">Semua Status</option>
                                    <option value="pending" {{ request('status_approval') == 'pending' ? 'selected' : '' }}>Menunggu</option>
                                    <option value="approved" {{ request('status_approval') == 'approved' ? 'selected' : '' }}>Disetujui</option>
                                    <option value="rejected" {{ request('status_approval') == 'rejected' ? 'selected' : '' }}>Ditolak</option>
                                </select>
                            </div>
                            <div class="col-md-12 mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-filter"></i> Filter
                                </button>
                                <a href="{{ route('admin.approval.index') }}" class="btn btn-secondary">
                                    <i class="bi bi-arrow-repeat"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Tanggal</th>
                                    <th>Karyawan</th>
                                    <th>Jenis</th>
                                    <th>Status</th>
                                    <th>Keterangan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($pengajuanPaginated as $item)
                                    <tr>
                                        <td>{{ $item->id }}</td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                @if(isset($item->tanggal_mulai) && isset($item->tanggal_selesai))
                                                    <span class="fw-bold">{{ \Carbon\Carbon::parse($item->tanggal_mulai)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($item->tanggal_selesai)->format('d/m/Y') }}</span>
                                                    <small class="text-muted">{{ $item->jumlah_hari ?? '1' }} hari</small>
                                                @elseif(isset($item->tanggal_awal) && isset($item->tanggal_akhir))
                                                    <span class="fw-bold">{{ \Carbon\Carbon::parse($item->tanggal_awal)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($item->tanggal_akhir)->format('d/m/Y') }}</span>
                                                    <small class="text-muted">{{ $item->jumlah_hari ?? '1' }} hari</small>
                                                @else
                                                    <span class="fw-bold">{{ \Carbon\Carbon::parse($item->tanggal)->format('d/m/Y') }}</span>
                                                    <small class="text-muted">{{ \Carbon\Carbon::parse($item->tanggal)->locale('id')->dayName }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>{{ $item->user->name }}</td>
                                        <td>
                                            @if($item->status == 'izin')
                                                <span class="badge bg-warning">Izin</span>
                                            @elseif($item->status == 'sakit')
                                                <span class="badge bg-info">Sakit</span>
                                            @elseif($item->status == 'cuti')
                                                <span class="badge bg-primary">Cuti</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($item->status_approval == 'pending')
                                                <span class="badge bg-secondary">Menunggu</span>
                                            @elseif($item->status_approval == 'approved')
                                                <span class="badge bg-success">Disetujui</span>
                                            @elseif($item->status_approval == 'rejected')
                                                <span class="badge bg-danger">Ditolak</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ $item->keterangan }}">
                                                {{ $item->keterangan }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ route('admin.approval.show', $item->id) }}" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i> Detail
                                                </a>
                                                @if($item->status_approval == 'pending')
                                                    <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#approveModal{{ $item->id }}">
                                                        <i class="bi bi-check-circle"></i> Setujui
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal{{ $item->id }}">
                                                        <i class="bi bi-x-circle"></i> Tolak
                                                    </button>
                                                @endif
                                            </div>

                                            <!-- Approve Modal -->
                                            <div class="modal fade" id="approveModal{{ $item->id }}" tabindex="-1" aria-labelledby="approveModalLabel{{ $item->id }}" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <form action="{{ route('admin.approval.approve', $item->id) }}" method="POST">
                                                            @csrf
                                                            <div class="modal-header bg-success text-white">
                                                                <h5 class="modal-title" id="approveModalLabel{{ $item->id }}">Setujui Pengajuan</h5>
                                                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <p>Anda yakin ingin menyetujui pengajuan ini?</p>
                                                                <div class="mb-3">
                                                                    <label for="approval_note" class="form-label">Catatan (Opsional)</label>
                                                                    <textarea class="form-control" id="approval_note" name="approval_note" rows="3"></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                                <button type="submit" class="btn btn-success">Setujui</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Reject Modal -->
                                            <div class="modal fade" id="rejectModal{{ $item->id }}" tabindex="-1" aria-labelledby="rejectModalLabel{{ $item->id }}" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <form action="{{ route('admin.approval.reject', $item->id) }}" method="POST">
                                                            @csrf
                                                            <div class="modal-header bg-danger text-white">
                                                                <h5 class="modal-title" id="rejectModalLabel{{ $item->id }}">Tolak Pengajuan</h5>
                                                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <p>Anda yakin ingin menolak pengajuan ini?</p>
                                                                <div class="mb-3">
                                                                    <label for="approval_note" class="form-label">Alasan Penolakan <span class="text-danger">*</span></label>
                                                                    <textarea class="form-control" id="approval_note" name="approval_note" rows="3" required></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                                <button type="submit" class="btn btn-danger">Tolak</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="bi bi-inbox fs-1 text-muted mb-2"></i>
                                                <h5>Tidak ada data pengajuan</h5>
                                                <p class="text-muted">Belum ada pengajuan izin/cuti/sakit yang tersedia</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $pengajuanPaginated->withQueryString()->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
