@extends('layouts.app')

@section('title', 'Riwayat Lembur')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-warning text-dark">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-clock me-2"></i>
                            Riwayat Lembur Saya
                        </h5>
                        <a href="{{ route('salary.index') }}" class="btn btn-dark btn-sm">
                            <i class="bi bi-arrow-left me-1"></i>
                            Kembali ke Slip Gaji
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filter -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form method="GET" action="{{ route('salary.overtime') }}" class="d-flex gap-2">
                                <select name="bulan" class="form-select">
                                    <option value="">Semua <PERSON>ulan</option>
                                    @for($i = 1; $i <= 12; $i++)
                                        <option value="{{ $i }}" {{ $bulan == $i ? 'selected' : '' }}>
                                            {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                        </option>
                                    @endfor
                                </select>
                                <select name="tahun" class="form-select">
                                    <option value="">Semua Tahun</option>
                                    @for($i = date('Y') - 2; $i <= date('Y'); $i++)
                                        <option value="{{ $i }}" {{ $tahun == $i ? 'selected' : '' }}>{{ $i }}</option>
                                    @endfor
                                </select>
                                <button type="submit" class="btn btn-outline-warning">
                                    <i class="bi bi-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="bi bi-clock display-6 text-warning"></i>
                                    <h4 class="mt-2">{{ $overtimes->sum('jam_lembur') }}</h4>
                                    <small class="text-muted">Total Jam Lembur</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="bi bi-check-circle display-6 text-success"></i>
                                    <h4 class="mt-2">{{ $overtimes->where('status', 'approved')->count() }}</h4>
                                    <small class="text-muted">Disetujui</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="bi bi-clock-history display-6 text-warning"></i>
                                    <h4 class="mt-2">{{ $overtimes->where('status', 'pending')->count() }}</h4>
                                    <small class="text-muted">Menunggu</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="bi bi-cash display-6 text-primary"></i>
                                    <h4 class="mt-2">Rp {{ number_format($overtimes->where('status', 'approved')->sum('total_upah_lembur'), 0, ',', '.') }}</h4>
                                    <small class="text-muted">Total Upah</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabel Lembur -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>No</th>
                                    <th>Tanggal</th>
                                    <th>Jam Pulang Normal</th>
                                    <th>Jam Keluar Aktual</th>
                                    <th>Jam Lembur</th>
                                    <th>Upah Lembur</th>
                                    <th>Status</th>
                                    <th>Keterangan</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($overtimes as $index => $overtime)
                                    <tr>
                                        <td>{{ $overtimes->firstItem() + $index }}</td>
                                        <td>
                                            <div class="fw-bold">{{ $overtime->tanggal->format('d/m/Y') }}</div>
                                            <small class="text-muted">{{ $overtime->tanggal->format('l') }}</small>
                                        </td>
                                        <td>{{ \Carbon\Carbon::parse($overtime->jam_pulang_normal)->format('H:i') }}</td>
                                        <td>{{ \Carbon\Carbon::parse($overtime->jam_keluar_aktual)->format('H:i') }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ $overtime->jam_lembur_format }}</span>
                                        </td>
                                        <td>
                                            <div>Rate: {{ $overtime->rate_lembur }}x</div>
                                            <small class="text-success fw-bold">Rp {{ number_format($overtime->total_upah_lembur, 0, ',', '.') }}</small>
                                        </td>
                                        <td>
                                            <span class="badge {{ $overtime->status_badge }}">{{ $overtime->status_text }}</span>
                                        </td>
                                        <td>
                                            @if($overtime->keterangan)
                                                <small>{{ $overtime->keterangan }}</small>
                                            @endif
                                            @if($overtime->catatan_admin)
                                                <div class="mt-1">
                                                    <small class="text-danger">
                                                        <i class="bi bi-exclamation-triangle me-1"></i>
                                                        {{ $overtime->catatan_admin }}
                                                    </small>
                                                </div>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="bi bi-inbox display-4"></i>
                                                <p class="mt-2">Belum ada riwayat lembur</p>
                                                <small>Lembur akan muncul otomatis saat Anda pulang melebihi jam kerja normal</small>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($overtimes->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $overtimes->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Info Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        Informasi Lembur
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Cara Kerja Sistem Lembur:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success me-2"></i>Lembur dihitung otomatis saat Anda pulang melebihi jam kerja normal</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Rate lembur adalah 1.5x dari upah per jam normal</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Lembur perlu persetujuan admin untuk masuk ke slip gaji</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Status Lembur:</h6>
                            <ul class="list-unstyled">
                                <li><span class="badge bg-warning me-2">Menunggu</span>Menunggu persetujuan admin</li>
                                <li><span class="badge bg-success me-2">Disetujui</span>Akan masuk ke slip gaji bulan ini</li>
                                <li><span class="badge bg-danger me-2">Ditolak</span>Tidak akan masuk ke slip gaji</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
