@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Form Absensi</h5>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <form method="POST" action="/absen" id="absenForm" enctype="multipart/form-data">
                        @csrf

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">Informasi Absensi</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status Kehadiran</label>
                                            <select name="status" id="status" class="form-select" required>
                                                <option value="hadir">Hadir</option>
                                                <option value="izin">Izin</option>
                                                <option value="sakit">Sakit</option>
                                                <option value="cuti">Cuti</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="tanggal" class="form-label">Tanggal</label>
                                            <input type="date" name="tanggal" id="tanggal" class="form-control" value="{{ date('Y-m-d') }}" readonly>
                                        </div>

                                        <div class="mb-3">
                                            <label for="jam" class="form-label">Jam</label>
                                            <input type="text" id="jam" class="form-control" value="{{ date('H:i:s') }}" readonly>
                                            <input type="hidden" name="jam_masuk" id="jam_masuk" value="{{ date('H:i:s') }}">
                                        </div>

                                        <div class="mb-3">
                                            <label for="lokasi" class="form-label">Lokasi</label>
                                            <div class="input-group">
                                                <input type="text" name="lokasi_masuk" id="lokasi" class="form-control" readonly>
                                                <button type="button" class="btn btn-primary" id="getLocation">
                                                    <i class="bi bi-geo-alt"></i> Dapatkan Lokasi
                                                </button>
                                            </div>
                                            <small class="text-muted">Klik tombol untuk mendapatkan lokasi Anda saat ini</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">Foto & Tanda Tangan</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="foto" class="form-label">Foto Selfie</label>
                                            <div class="d-flex flex-column align-items-center">
                                                <div class="video-container mb-2">
                                                    <video id="video" width="100%" height="200" autoplay></video>
                                                </div>
                                                <div class="d-flex gap-2">
                                                    <button type="button" id="captureBtn" class="btn btn-primary">
                                                        <i class="bi bi-camera"></i> Ambil Foto
                                                    </button>
                                                    <button type="button" id="retakeBtn" class="btn btn-secondary" style="display:none;">
                                                        <i class="bi bi-arrow-repeat"></i> Ambil Ulang
                                                    </button>
                                                </div>
                                                <canvas id="canvas" style="display:none;"></canvas>
                                                <input type="hidden" name="foto_masuk" id="foto_masuk" value="placeholder">
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="signature-pad" class="form-label">Tanda Tangan Digital</label>
                                            <div class="border rounded p-2">
                                                <canvas id="signature-pad" class="signature-pad" width="100%" height="200"></canvas>
                                            </div>
                                            <div class="d-flex justify-content-end mt-2">
                                                <button type="button" id="clear-signature" class="btn btn-sm btn-secondary">
                                                    <i class="bi bi-eraser"></i> Hapus
                                                </button>
                                            </div>
                                            <input type="hidden" name="tanda_tangan" id="tanda_tangan">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="keterangan" class="form-label">Keterangan</label>
                            <textarea name="keterangan" id="keterangan" class="form-control" rows="3" placeholder="Tambahkan keterangan jika diperlukan"></textarea>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="/dashboard" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Kembali
                            </a>
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                <i class="bi bi-check-circle"></i> Kirim Absensi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update jam secara real-time
    setInterval(function() {
        const now = new Date();
        const timeString = now.getHours().toString().padStart(2, '0') + ':' +
                          now.getMinutes().toString().padStart(2, '0') + ':' +
                          now.getSeconds().toString().padStart(2, '0');
        document.getElementById('jam').value = timeString;
        document.getElementById('jam_masuk').value = timeString;
    }, 1000);

    // Geolocation
    document.getElementById('getLocation').addEventListener('click', function() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                document.getElementById('lokasi').value = `${lat}, ${lng}`;
            }, function(error) {
                alert('Error mendapatkan lokasi: ' + error.message);
            });
        } else {
            alert('Geolocation tidak didukung oleh browser Anda');
        }
    });

    // Webcam capture
    let video = document.getElementById('video');
    let canvas = document.getElementById('canvas');
    let captureBtn = document.getElementById('captureBtn');
    let retakeBtn = document.getElementById('retakeBtn');
    let fotoInput = document.getElementById('foto_masuk');
    let stream;

    // Akses kamera
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(function(mediaStream) {
                stream = mediaStream;
                video.srcObject = mediaStream;
                video.play();
            })
            .catch(function(error) {
                console.error('Tidak dapat mengakses kamera:', error);
            });
    }

    // Ambil foto
    captureBtn.addEventListener('click', function() {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height);
        const imgData = canvas.toDataURL('image/png');
        fotoInput.value = imgData;
        video.style.display = 'none';
        canvas.style.display = 'block';
        captureBtn.style.display = 'none';
        retakeBtn.style.display = 'inline-block';
    });

    // Ambil ulang foto
    retakeBtn.addEventListener('click', function() {
        video.style.display = 'block';
        canvas.style.display = 'none';
        captureBtn.style.display = 'inline-block';
        retakeBtn.style.display = 'none';
        fotoInput.value = '';
    });

    // Tanda tangan digital
    const signaturePad = new SignaturePad(document.getElementById('signature-pad'), {
        backgroundColor: 'rgba(255, 255, 255, 0)',
        penColor: 'black'
    });

    document.getElementById('clear-signature').addEventListener('click', function() {
        signaturePad.clear();
    });

    // Form submit
    document.getElementById('absenForm').addEventListener('submit', function(e) {
        if (signaturePad.isEmpty()) {
            e.preventDefault();
            alert('Silakan isi tanda tangan Anda');
            return false;
        }

        if (!fotoInput.value) {
            e.preventDefault();
            alert('Silakan ambil foto selfie Anda');
            return false;
        }

        if (!document.getElementById('lokasi').value) {
            e.preventDefault();
            alert('Silakan dapatkan lokasi Anda');
            return false;
        }

        // Simpan tanda tangan sebagai base64
        document.getElementById('tanda_tangan').value = signaturePad.toDataURL();
    });
});
</script>

<style>
.signature-pad {
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    height: 200px;
    background-color: white;
}

.video-container {
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 4px;
    overflow: hidden;
}

#canvas {
    width: 100%;
    height: 200px;
    border: 1px solid #ccc;
    border-radius: 4px;
}
</style>
@endsection
