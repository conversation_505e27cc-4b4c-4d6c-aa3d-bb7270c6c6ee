<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Absensi App</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Google Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap + Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

    <!-- Signature Pad -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/signature_pad/1.5.3/signature_pad.min.js"></script>

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        .navbar {
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }
        .btn {
            transition: all 0.3s ease-in-out;
        }
        .btn:hover {
            transform: scale(1.03);
        }
        footer {
            margin-top: 50px;
            text-align: center;
            color: #777;
            font-size: 14px;
        }

        /* Fix for login and register pages */
        body.auth-page {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            width: 100%;
            height: 100vh;
        }

        body.auth-page .container.my-4 {
            margin: 0 !important;
            padding: 0 !important;
            max-width: 100% !important;
            width: 100% !important;
        }

        /* Navbar Styles */
        .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 0.375rem;
        }

        .navbar-nav .dropdown-item.active {
            background-color: #0d6efd;
            color: white;
        }

        .navbar-nav .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .navbar-nav .dropdown-item.active:hover {
            background-color: #0b5ed7;
        }

        .nav-link {
            font-weight: 500;
            white-space: nowrap;
        }

        .dropdown-menu {
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            min-width: 200px;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .dropdown-item .badge {
            font-size: 0.7rem;
            float: right;
            margin-top: 0.125rem;
        }

        .nav-link .badge {
            margin-left: 0.25rem;
            font-size: 0.75em;
        }

        /* Navbar responsive improvements */
        @media (max-width: 991.98px) {
            .navbar-nav {
                padding-top: 1rem;
            }

            .navbar-nav .nav-item {
                margin-bottom: 0.25rem;
            }

            .navbar-nav .dropdown-menu {
                border: none;
                box-shadow: none;
                background-color: rgba(255, 255, 255, 0.05);
                margin-left: 1rem;
            }

            .navbar-nav .dropdown-item {
                color: rgba(255, 255, 255, 0.8);
                padding: 0.5rem 1rem;
            }

            .navbar-nav .dropdown-item:hover {
                background-color: rgba(255, 255, 255, 0.1);
                color: white;
            }
        }

        @media (max-width: 768px) {
            .navbar-brand {
                font-size: 1.25rem;
            }

            .nav-link {
                font-size: 0.9rem;
            }
        }
    </style>

    <script>
        // Add auth-page class to body if on login or register page
        document.addEventListener('DOMContentLoaded', function() {
            const path = window.location.pathname;
            if (path === '/login' || path === '/register') {
                document.body.classList.add('auth-page');
            }
        });
    </script>
</head>
<body>

    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
        <div class="container py-2">
            <a class="navbar-brand fw-bold" href="{{ auth()->check() ? (auth()->user()->role === 'admin' ? route('admin.dashboard') : route('dashboard')) : route('home') }}">Absensi</a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent" aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarContent">
                @auth
                    @if(auth()->user()->role === 'admin')
                        <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}">
                                    <i class="bi bi-speedometer2 me-1"></i> Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}" href="{{ route('admin.users.index') }}">
                                    <i class="bi bi-people me-1"></i> Karyawan
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.absensi') || request()->routeIs('admin.approval.*') || request()->routeIs('admin.manual.attendance.requests*') ? 'active' : '' }}" href="#" id="absensiDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-calendar-check me-1"></i> Absensi
                                    @php
                                        $totalPending = \App\Models\Absensi::whereIn('status', ['izin', 'sakit', 'cuti'])->where('status_approval', 'pending')->count() + \App\Models\ManualAttendanceRequest::where('status', 'pending')->count();
                                    @endphp
                                    @if($totalPending > 0)
                                        <span class="badge bg-danger rounded-pill">{{ $totalPending }}</span>
                                    @endif
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="absensiDropdown">
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('admin.absensi') ? 'active' : '' }}" href="{{ route('admin.absensi') }}">
                                            <i class="bi bi-calendar-check me-2"></i> Data Absensi
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('admin.approval.*') ? 'active' : '' }}" href="{{ route('admin.approval.index') }}">
                                            <i class="bi bi-check-square me-2"></i> Persetujuan Izin
                                            @php
                                                $pendingCount = \App\Models\Absensi::whereIn('status', ['izin', 'sakit', 'cuti'])->where('status_approval', 'pending')->count();
                                            @endphp
                                            @if($pendingCount > 0)
                                                <span class="badge bg-danger rounded-pill ms-1">{{ $pendingCount }}</span>
                                            @endif
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('admin.manual.attendance.requests*') ? 'active' : '' }}" href="{{ route('admin.manual.attendance.requests') }}">
                                            <i class="bi bi-person-raised-hand me-2"></i> Absen Manual
                                            @php
                                                $manualPendingCount = \App\Models\ManualAttendanceRequest::where('status', 'pending')->count();
                                            @endphp
                                            @if($manualPendingCount > 0)
                                                <span class="badge bg-warning rounded-pill ms-1">{{ $manualPendingCount }}</span>
                                            @endif
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('admin.attendance*') ? 'active' : '' }}" href="{{ route('admin.attendance.index') }}">
                                            <i class="bi bi-calendar-edit me-2"></i> Kelola Kehadiran
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.rekap*') ? 'active' : '' }}" href="#" id="rekapDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-table me-1"></i> Rekap
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="rekapDropdown">
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('admin.rekap') && !request()->routeIs('admin.rekap.izin') ? 'active' : '' }}" href="{{ route('admin.rekap') }}">
                                            <i class="bi bi-calendar-check me-2"></i> Rekap Absensi
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('admin.rekap.izin') ? 'active' : '' }}" href="{{ route('admin.rekap.izin') }}">
                                            <i class="bi bi-calendar-minus me-2"></i> Rekap Izin/Cuti/Sakit
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle {{ request()->routeIs('admin.salary*') || request()->routeIs('admin.overtime*') ? 'active' : '' }}" href="#" id="salaryDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-cash-stack me-1"></i> Gaji
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="salaryDropdown">
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('admin.salary*') ? 'active' : '' }}" href="{{ route('admin.salary.index') }}">
                                            <i class="bi bi-receipt me-2"></i> Manajemen Gaji
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('admin.overtime*') ? 'active' : '' }}" href="{{ route('admin.overtime.index') }}">
                                            <i class="bi bi-clock me-2"></i> Manajemen Lembur
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.settings') ? 'active' : '' }}" href="{{ route('admin.settings') }}">
                                    <i class="bi bi-gear me-1"></i> Pengaturan
                                </a>
                            </li>
                        </ul>
                    @else
                        <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                                    <i class="bi bi-house me-1"></i> Dashboard
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle {{ request()->routeIs('absen.riwayat') || request()->routeIs('absen.pengajuan') ? 'active' : '' }}" href="#" id="riwayatDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-clock-history me-1"></i> Riwayat
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="riwayatDropdown">
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('absen.riwayat') ? 'active' : '' }}" href="{{ route('absen.riwayat') }}">
                                            <i class="bi bi-calendar-check me-2"></i> Riwayat Absensi
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('absen.pengajuan') ? 'active' : '' }}" href="{{ route('absen.pengajuan') }}">
                                            <i class="bi bi-calendar-minus me-2"></i> Riwayat Pengajuan
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('izin.form') ? 'active' : '' }}" href="{{ route('izin.form') }}">
                                    <i class="bi bi-calendar-plus me-1"></i> Pengajuan Izin
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle {{ request()->routeIs('salary*') ? 'active' : '' }}" href="#" id="userSalaryDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-cash-coin me-1"></i> Gaji
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="userSalaryDropdown">
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('salary.index') ? 'active' : '' }}" href="{{ route('salary.index') }}">
                                            <i class="bi bi-receipt me-2"></i> Slip Gaji
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('salary.overtime') ? 'active' : '' }}" href="{{ route('salary.overtime') }}">
                                            <i class="bi bi-clock me-2"></i> Riwayat Lembur
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('salary.deductions') ? 'active' : '' }}" href="{{ route('salary.deductions') }}">
                                            <i class="bi bi-dash-circle me-2"></i> Riwayat Potongan
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    @endif
                @endauth

                <div class="d-flex align-items-center gap-2 ms-auto">
                    @guest
                        <a href="{{ route('login') }}" class="btn btn-sm btn-success">
                            <i class="bi bi-box-arrow-in-right me-1"></i> Login
                        </a>
                        <a href="{{ route('register') }}" class="btn btn-sm btn-outline-light">
                            <i class="bi bi-person-plus me-1"></i> Register
                        </a>
                    @endguest

                    @auth
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle me-1"></i>{{ Auth::user()->name }}
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="{{ route('profile.show') }}"><i class="bi bi-person me-2"></i>Profil Saya</a></li>
                                <li><a class="dropdown-item" href="{{ route('profile.edit') }}"><i class="bi bi-pencil-square me-2"></i>Edit Profil</a></li>
                                <li><a class="dropdown-item" href="{{ route('profile.password.edit') }}"><i class="bi bi-key me-2"></i>Ganti Password</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button class="dropdown-item text-danger">
                                            <i class="bi bi-box-arrow-right me-2"></i>Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Content -->
    <div class="container my-4">
        @if ($errors->any() && !request()->is('login') && !request()->is('register'))
            <div class="alert alert-danger shadow-sm">
                {{ $errors->first() }}
            </div>
        @endif

        @if (session('success') && !request()->is('login') && !request()->is('register'))
            <div class="alert alert-success shadow-sm">
                {{ session('success') }}
            </div>
        @endif

        @yield('content')
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Stack for additional scripts -->
    @stack('scripts')
</body>
</html>
