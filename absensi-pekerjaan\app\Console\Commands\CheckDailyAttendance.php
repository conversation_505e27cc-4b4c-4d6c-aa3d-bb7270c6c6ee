<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Absensi;
use App\Models\SalaryDeduction;
use App\Models\Setting;
use Carbon\Carbon;

class CheckDailyAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:check-daily {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check daily attendance and mark absent employees as alpha';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $date = $this->argument('date') ? Carbon::parse($this->argument('date')) : Carbon::yesterday();

        // Skip weekend jika diatur
        if ($this->shouldSkipWeekend($date)) {
            $this->info("Skipping weekend: {$date->format('Y-m-d')} ({$date->format('l')})");
            return;
        }

        $this->info("Checking attendance for: {$date->format('Y-m-d')} ({$date->format('l')})");

        // Update keterangan yang salah terlebih dahulu
        $this->updateIncorrectKeterangan();

        $users = User::where('role', 'user')->get();
        $alphaCount = 0;
        $processedCount = 0;

        foreach ($users as $user) {
            $processedCount++;

            // Cek apakah user sudah ada record absensi untuk tanggal tersebut
            $existingAbsensi = Absensi::where('user_id', $user->id)
                ->whereDate('tanggal', $date->format('Y-m-d'))
                ->first();

            if (!$existingAbsensi) {
                // Cek apakah user memiliki izin/cuti/sakit yang disetujui untuk tanggal ini
                $hasApprovedLeave = $this->hasApprovedLeaveForDate($user, $date);

                if ($hasApprovedLeave) {
                    $this->line("  - {$user->name}: Has approved leave, skipping alpha marking");
                } else {
                    // Buat record alpha
                    $this->createAlphaRecord($user, $date);
                    $alphaCount++;
                    $this->line("  - {$user->name}: Marked as ALPHA");
                }
            } else {
                $this->line("  - {$user->name}: Already has attendance record ({$existingAbsensi->status})");
            }
        }

        $this->info("\nSummary:");
        $this->info("- Total employees processed: {$processedCount}");
        $this->info("- Marked as alpha: {$alphaCount}");

        if ($alphaCount > 0) {
            $this->warn("- Alpha employees will have salary deductions applied");
        }
    }

    private function updateIncorrectKeterangan()
    {
        // Update keterangan yang mengandung "Auto-generated" untuk status hadir
        $updated = Absensi::where('status', 'hadir')
            ->where('keterangan', 'LIKE', '%Auto-generated%')
            ->update(['keterangan' => null]);

        if ($updated > 0) {
            $this->line("Updated {$updated} incorrect keterangan for 'hadir' status");
        }

        // Update keterangan yang mengandung "Auto-generated" untuk status alpha
        $updatedAlpha = Absensi::where('status', 'alpha')
            ->where('keterangan', 'LIKE', '%Auto-generated%')
            ->update(['keterangan' => 'Tidak hadir tanpa keterangan']);

        if ($updatedAlpha > 0) {
            $this->line("Updated {$updatedAlpha} incorrect keterangan for 'alpha' status");
        }
    }

    private function shouldSkipWeekend($date)
    {
        $skipWeekend = Setting::getValue('skip_weekend_attendance', 'true');

        if ($skipWeekend === 'true') {
            return $date->isWeekend();
        }

        return false;
    }

    private function hasApprovedLeaveForDate($user, $date)
    {
        // Cek apakah user memiliki pengajuan izin/cuti/sakit yang disetujui untuk tanggal ini
        $approvedLeave = \App\Models\IzinCuti::where('user_id', $user->id)
            ->where('status', 'approved')
            ->where('tanggal_mulai', '<=', $date->format('Y-m-d'))
            ->where('tanggal_selesai', '>=', $date->format('Y-m-d'))
            ->first();

        return $approvedLeave !== null;
    }

    private function createAlphaRecord($user, $date)
    {
        // Buat record absensi alpha
        $absensi = Absensi::create([
            'user_id' => $user->id,
            'tanggal' => $date->format('Y-m-d'),
            'jam_masuk' => null,
            'jam_keluar' => null,
            'keterangan' => 'Tidak hadir tanpa keterangan',
            'status' => 'alpha',
            'status_approval' => 'approved', // Auto approved untuk alpha
            'lokasi_masuk' => 'System Generated',
            'lokasi_keluar' => 'System Generated',
            'foto_masuk' => null,
            'foto_keluar' => null,
            'tanda_tangan' => null,
            'catatan' => 'Sistem otomatis menandai sebagai alpha karena tidak ada absensi pada ' . $date->format('d/m/Y'),
            'approval_at' => now(),
            'approved_by' => 1 // Assuming admin user ID is 1
        ]);

        // Buat potongan gaji untuk alpha
        $this->createAlphaDeduction($user, $date, $absensi);

        // Update jumlah SP jika perlu
        $this->checkAndUpdateWarningLetters($user, $date);
    }

    private function createAlphaDeduction($user, $date, $absensi)
    {
        $potonganAlpha = (float)Setting::getValue('potongan_alpha', 100000);

        SalaryDeduction::create([
            'user_id' => $user->id,
            'tanggal' => $date->format('Y-m-d'),
            'bulan' => $date->month,
            'tahun' => $date->year,
            'jenis' => 'alpha',
            'jumlah_potongan' => $potonganAlpha,
            'keterangan' => 'Potongan alpha/tanpa keterangan pada ' . $date->format('d/m/Y'),
            'absensi_id' => $absensi->id,
            'status' => 'approved',
            'dibuat_oleh' => 1, // System
            'disetujui_oleh' => 1 // Auto approved
        ]);
    }

    private function checkAndUpdateWarningLetters($user, $date)
    {
        // Hitung total alpha dalam bulan ini
        $alphaThisMonth = Absensi::where('user_id', $user->id)
            ->where('status', 'alpha')
            ->whereMonth('tanggal', $date->month)
            ->whereYear('tanggal', $date->year)
            ->count();

        // Ambil batas untuk SP
        $batasAlphaSP1 = (int)Setting::getValue('batas_alpha_sp1', 3);
        $batasAlphaSP2 = (int)Setting::getValue('batas_alpha_sp2', 5);
        $batasAlphaSP3 = (int)Setting::getValue('batas_alpha_sp3', 7);

        $currentSP = $user->jumlah_sp;

        // Cek apakah perlu SP baru
        if ($alphaThisMonth >= $batasAlphaSP3 && $currentSP < 3) {
            $this->issueSP($user, 'sp3', $date);
        } elseif ($alphaThisMonth >= $batasAlphaSP2 && $currentSP < 2) {
            $this->issueSP($user, 'sp2', $date);
        } elseif ($alphaThisMonth >= $batasAlphaSP1 && $currentSP < 1) {
            $this->issueSP($user, 'sp1', $date);
        }
    }

    private function issueSP($user, $spType, $date)
    {
        $spNumber = (int)str_replace('sp', '', $spType);

        // Update user SP
        $user->update([
            'jumlah_sp' => $spNumber,
            'tanggal_sp_terakhir' => $date->format('Y-m-d')
        ]);

        // Buat potongan gaji untuk SP
        $potonganSP = (float)Setting::getValue('potongan_' . $spType, 100000);

        SalaryDeduction::create([
            'user_id' => $user->id,
            'tanggal' => $date->format('Y-m-d'),
            'bulan' => $date->month,
            'tahun' => $date->year,
            'jenis' => $spType,
            'jumlah_potongan' => $potonganSP,
            'keterangan' => 'Surat Peringatan ' . $spNumber . ' karena alpha berulang',
            'status' => 'approved',
            'dibuat_oleh' => 1,
            'disetujui_oleh' => 1
        ]);

        $this->warn("  - {$user->name}: Issued {$spType} (Total alpha this month: " .
                   Absensi::where('user_id', $user->id)
                          ->where('status', 'alpha')
                          ->whereMonth('tanggal', $date->month)
                          ->whereYear('tanggal', $date->year)
                          ->count() . ")");
    }
}
