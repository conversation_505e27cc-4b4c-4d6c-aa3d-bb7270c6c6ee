@extends('layouts.app')

@section('title', 'Riwayat Potongan Gaji')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-danger text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-dash-circle me-2"></i>
                            Riwayat Potongan Gaji
                        </h5>
                        <a href="{{ route('salary.index') }}" class="btn btn-light btn-sm">
                            <i class="bi bi-arrow-left me-1"></i>
                            Kembali ke Slip Gaji
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filter -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form method="GET" action="{{ route('salary.deductions') }}" class="d-flex gap-2">
                                <select name="bulan" class="form-select">
                                    <option value="">Semua Bulan</option>
                                    @for($i = 1; $i <= 12; $i++)
                                        <option value="{{ $i }}" {{ $bulan == $i ? 'selected' : '' }}>
                                            {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                        </option>
                                    @endfor
                                </select>
                                <select name="tahun" class="form-select">
                                    <option value="">Semua Tahun</option>
                                    @for($i = date('Y') - 2; $i <= date('Y'); $i++)
                                        <option value="{{ $i }}" {{ $tahun == $i ? 'selected' : '' }}>{{ $i }}</option>
                                    @endfor
                                </select>
                                <button type="submit" class="btn btn-outline-danger">
                                    <i class="bi bi-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="bi bi-clock-history display-6 text-warning"></i>
                                    <h4 class="mt-2">{{ $deductions->where('jenis', 'terlambat')->count() }}</h4>
                                    <small class="text-muted">Keterlambatan</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <i class="bi bi-x-circle display-6 text-danger"></i>
                                    <h4 class="mt-2">{{ $deductions->where('jenis', 'alpha')->count() }}</h4>
                                    <small class="text-muted">Alpha</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="bi bi-exclamation-triangle display-6 text-info"></i>
                                    <h4 class="mt-2">{{ $deductions->whereIn('jenis', ['sp1', 'sp2', 'sp3'])->count() }}</h4>
                                    <small class="text-muted">Surat Peringatan</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-dark">
                                <div class="card-body text-center">
                                    <i class="bi bi-cash display-6 text-dark"></i>
                                    <h4 class="mt-2">Rp {{ number_format($deductions->where('status', 'approved')->sum('jumlah_potongan'), 0, ',', '.') }}</h4>
                                    <small class="text-muted">Total Potongan</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabel Potongan -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>No</th>
                                    <th>Tanggal</th>
                                    <th>Jenis Potongan</th>
                                    <th>Jumlah Potongan</th>
                                    <th>Keterangan</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($deductions as $index => $deduction)
                                    <tr>
                                        <td>{{ $deductions->firstItem() + $index }}</td>
                                        <td>
                                            <div class="fw-bold">{{ $deduction->tanggal->format('d/m/Y') }}</div>
                                            <small class="text-muted">{{ $deduction->tanggal->format('l') }}</small>
                                        </td>
                                        <td>
                                            <span class="badge {{ $deduction->jenis_badge }}">{{ $deduction->jenis_text }}</span>
                                        </td>
                                        <td>
                                            <span class="text-danger fw-bold">Rp {{ number_format($deduction->jumlah_potongan, 0, ',', '.') }}</span>
                                        </td>
                                        <td>
                                            <div>{{ $deduction->keterangan }}</div>
                                            @if($deduction->catatan)
                                                <small class="text-muted">
                                                    <i class="bi bi-chat-text me-1"></i>
                                                    {{ $deduction->catatan }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge {{ $deduction->status_badge }}">{{ $deduction->status_text }}</span>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="bi bi-emoji-smile display-4"></i>
                                                <p class="mt-2">Tidak ada potongan gaji</p>
                                                <small>Pertahankan kedisiplinan untuk menghindari potongan gaji</small>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($deductions->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $deductions->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Info Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        Informasi Potongan Gaji
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Jenis Potongan:</h6>
                            <ul class="list-unstyled">
                                <li><span class="badge bg-warning me-2">Keterlambatan</span>Potongan per hari terlambat</li>
                                <li><span class="badge bg-danger me-2">Alpha</span>Potongan per hari tanpa keterangan</li>
                                <li><span class="badge bg-info me-2">SP 1</span>Surat Peringatan Pertama</li>
                                <li><span class="badge bg-warning me-2">SP 2</span>Surat Peringatan Kedua</li>
                                <li><span class="badge bg-danger me-2">SP 3</span>Surat Peringatan Ketiga</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Tips Menghindari Potongan:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success me-2"></i>Datang tepat waktu setiap hari</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Ajukan izin jika tidak bisa hadir</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Patuhi peraturan perusahaan</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>Komunikasi yang baik dengan atasan</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="bi bi-lightbulb me-2"></i>
                                <strong>Catatan:</strong> Potongan gaji akan otomatis dipotong dari slip gaji bulanan Anda. 
                                Jika ada keberatan terhadap potongan tertentu, silakan hubungi bagian HRD.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
