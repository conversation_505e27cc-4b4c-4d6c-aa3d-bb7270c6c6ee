<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><PERSON><PERSON><PERSON> - {{ $periodeName }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #1e40af;
            padding-bottom: 20px;
        }
        
        .company-name {
            font-size: 20px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 5px;
        }
        
        .company-address {
            font-size: 12px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .report-title {
            font-size: 18px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 5px;
        }
        
        .report-period {
            font-size: 14px;
            color: #666;
        }
        
        .stats-section {
            margin: 20px 0;
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .stats-title {
            font-size: 14px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 10px;
        }
        
        .stats-grid {
            display: table;
            width: 100%;
        }
        
        .stats-row {
            display: table-row;
        }
        
        .stats-cell {
            display: table-cell;
            padding: 5px 15px;
            border-right: 1px solid #e2e8f0;
            text-align: center;
        }
        
        .stats-cell:last-child {
            border-right: none;
        }
        
        .stats-label {
            font-weight: bold;
            color: #374151;
            font-size: 11px;
        }
        
        .stats-value {
            font-size: 14px;
            font-weight: bold;
            color: #dc2626;
        }
        
        .table-container {
            margin-top: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 11px;
        }
        
        th {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            border: 1px solid #1e40af;
        }
        
        td {
            padding: 10px 8px;
            border: 1px solid #e2e8f0;
            vertical-align: middle;
        }
        
        tr:nth-child(even) {
            background-color: #f8fafc;
        }
        
        tr:hover {
            background-color: #e0f2fe;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }
        
        .badge-danger { background-color: #dc2626; }
        .badge-warning { background-color: #f59e0b; }
        .badge-info { background-color: #0ea5e9; }
        .badge-primary { background-color: #3b82f6; }
        .badge-dark { background-color: #374151; }
        .badge-secondary { background-color: #6b7280; }
        .badge-success { background-color: #10b981; }
        
        .amount {
            font-weight: bold;
            color: #dc2626;
            text-align: right;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #e2e8f0;
            padding-top: 15px;
        }
        
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }
        
        .employee-info {
            display: flex;
            align-items: center;
        }
        
        .employee-name {
            font-weight: bold;
            color: #1f2937;
        }
        
        .employee-position {
            font-size: 10px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ $namaPerusahaan }}</div>
        <div class="company-address">{{ $alamatPerusahaan }}</div>
        <div class="report-title">LAPORAN POTONGAN GAJI</div>
        <div class="report-period">Periode: {{ $periodeName }}</div>
    </div>

    <!-- Statistics -->
    <div class="stats-section">
        <div class="stats-title">📊 Ringkasan Potongan Gaji</div>
        <div class="stats-grid">
            <div class="stats-row">
                <div class="stats-cell">
                    <div class="stats-label">Total Potongan</div>
                    <div class="stats-value">Rp {{ number_format($stats['total_amount']) }}</div>
                    <div style="font-size: 10px; color: #666;">{{ $stats['total_count'] }} item</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-label">Alpha</div>
                    <div class="stats-value">{{ $stats['alpha_count'] }}</div>
                    <div style="font-size: 10px; color: #666;">Rp {{ number_format($stats['alpha_amount']) }}</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-label">Terlambat</div>
                    <div class="stats-value">{{ $stats['terlambat_count'] }}</div>
                    <div style="font-size: 10px; color: #666;">Rp {{ number_format($stats['terlambat_amount']) }}</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-label">Surat Peringatan</div>
                    <div class="stats-value">{{ $stats['sp_count'] }}</div>
                    <div style="font-size: 10px; color: #666;">Rp {{ number_format($stats['sp_amount']) }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Table -->
    <div class="table-container">
        @if($allDeductions->count() > 0)
            <table>
                <thead>
                    <tr>
                        <th style="width: 5%;">No</th>
                        <th style="width: 25%;">Karyawan</th>
                        <th style="width: 12%;">Tanggal</th>
                        <th style="width: 12%;">Jenis</th>
                        <th style="width: 15%;">Jumlah Potongan</th>
                        <th style="width: 20%;">Keterangan</th>
                        <th style="width: 11%;">Status</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($allDeductions as $index => $deduction)
                        <tr>
                            <td style="text-align: center;">{{ $index + 1 }}</td>
                            <td>
                                <div class="employee-name">{{ $deduction->user->name }}</div>
                                <div class="employee-position">{{ $deduction->user->jabatan ?? 'Karyawan' }}</div>
                            </td>
                            <td style="text-align: center;">{{ \Carbon\Carbon::parse($deduction->tanggal)->format('d/m/Y') }}</td>
                            <td style="text-align: center;">
                                @php
                                    $jenisColors = [
                                        'alpha' => 'danger',
                                        'terlambat' => 'warning',
                                        'sp1' => 'info',
                                        'sp2' => 'primary',
                                        'sp3' => 'dark',
                                        'lainnya' => 'secondary'
                                    ];
                                    $color = $jenisColors[$deduction->jenis] ?? 'secondary';
                                    $jenisLabels = [
                                        'alpha' => 'Alpha',
                                        'terlambat' => 'Terlambat',
                                        'sp1' => 'SP 1',
                                        'sp2' => 'SP 2',
                                        'sp3' => 'SP 3',
                                        'lainnya' => 'Lainnya'
                                    ];
                                    $label = $jenisLabels[$deduction->jenis] ?? ucfirst($deduction->jenis);
                                @endphp
                                <span class="badge badge-{{ $color }}">{{ $label }}</span>
                            </td>
                            <td class="amount">Rp {{ number_format($deduction->jumlah_potongan) }}</td>
                            <td>{{ $deduction->keterangan ?: '-' }}</td>
                            <td style="text-align: center;">
                                @if($deduction->status == 'approved')
                                    <span class="badge badge-success">Disetujui</span>
                                @elseif($deduction->status == 'pending')
                                    <span class="badge badge-warning">Pending</span>
                                @else
                                    <span class="badge badge-danger">Ditolak</span>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @else
            <div class="no-data">
                <strong>Tidak ada data potongan gaji untuk periode ini</strong>
            </div>
        @endif
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>Laporan ini digenerate secara otomatis pada {{ date('d/m/Y H:i:s') }} WIB</p>
        <p>{{ $namaPerusahaan }} - Sistem Manajemen Absensi</p>
    </div>
</body>
</html>
