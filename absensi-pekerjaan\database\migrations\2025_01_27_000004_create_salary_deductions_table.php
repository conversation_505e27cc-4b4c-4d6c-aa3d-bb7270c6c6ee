<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('salary_deductions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->date('tanggal');
            $table->integer('bulan');
            $table->integer('tahun');
            
            // Jen<PERSON> potongan
            $table->enum('jenis', [
                'terlambat', 
                'alpha', 
                'sp1', 
                'sp2', 
                'sp3', 
                'lainnya'
            ]);
            
            // Detail potongan
            $table->decimal('jumlah_potongan', 15, 2);
            $table->text('keterangan');
            $table->text('catatan')->nullable();
            
            // Referensi ke data terkait
            $table->foreignId('absensi_id')->nullable()->constrained('absensis')->onDelete('set null');
            $table->json('data_pendukung')->nullable(); // Data tambahan dalam format JSON
            
            // Status dan approval
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('approved');
            $table->timestamp('tanggal_disetujui')->nullable();
            $table->unsignedBigInteger('dibuat_oleh')->nullable();
            $table->unsignedBigInteger('disetujui_oleh')->nullable();
            
            $table->timestamps();
            
            // Index
            $table->index(['user_id', 'bulan', 'tahun']);
            $table->index(['tanggal', 'jenis']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('salary_deductions');
    }
};
