@extends('layouts.app')

@section('content')
<div class="container">
    <div class="card">
        <div class="card-header bg-dark text-white">
            <h5 class="mb-0">Edit Pengguna</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.users.update', $user->id) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="mb-3">
                    <label for="name" class="form-label">Nama</label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $user->name) }}" required>
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', $user->email) }}" required>
                    @error('email')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">Password (Kosongkan jika tidak ingin mengubah)</label>
                    <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password">
                    @error('password')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="password_confirmation" class="form-label">Konfirmasi Password</label>
                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation">
                </div>

                <div class="mb-3">
                    <label for="role" class="form-label">Role</label>
                    <select class="form-select @error('role') is-invalid @enderror" id="role" name="role" required>
                        <option value="">Pilih Role</option>
                        <option value="admin" {{ (old('role', $user->role) == 'admin') ? 'selected' : '' }}>Admin</option>
                        <option value="user" {{ (old('role', $user->role) == 'user') ? 'selected' : '' }}>User</option>
                    </select>
                    @error('role')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Informasi Gaji (hanya untuk user) -->
                <div id="salarySection" style="{{ $user->role == 'user' ? '' : 'display: none;' }}">
                    <hr>
                    <h6 class="text-primary mb-3">Informasi Gaji</h6>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="gaji_pokok" class="form-label">Gaji Pokok</label>
                                <input type="number" class="form-control @error('gaji_pokok') is-invalid @enderror"
                                       id="gaji_pokok" name="gaji_pokok" value="{{ old('gaji_pokok', $user->gaji_pokok) }}" min="0">
                                @error('gaji_pokok')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tunjangan_transport" class="form-label">Tunjangan Transport</label>
                                <input type="number" class="form-control @error('tunjangan_transport') is-invalid @enderror"
                                       id="tunjangan_transport" name="tunjangan_transport" value="{{ old('tunjangan_transport', $user->tunjangan_transport) }}" min="0">
                                @error('tunjangan_transport')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tunjangan_makan" class="form-label">Tunjangan Makan</label>
                                <input type="number" class="form-control @error('tunjangan_makan') is-invalid @enderror"
                                       id="tunjangan_makan" name="tunjangan_makan" value="{{ old('tunjangan_makan', $user->tunjangan_makan) }}" min="0">
                                @error('tunjangan_makan')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tunjangan_lainnya" class="form-label">Tunjangan Lainnya</label>
                                <input type="number" class="form-control @error('tunjangan_lainnya') is-invalid @enderror"
                                       id="tunjangan_lainnya" name="tunjangan_lainnya" value="{{ old('tunjangan_lainnya', $user->tunjangan_lainnya) }}" min="0">
                                @error('tunjangan_lainnya')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">Kembali</a>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.getElementById('role').addEventListener('change', function() {
    const salarySection = document.getElementById('salarySection');
    if (this.value === 'user') {
        salarySection.style.display = 'block';
    } else {
        salarySection.style.display = 'none';
    }
});
</script>
@endsection
