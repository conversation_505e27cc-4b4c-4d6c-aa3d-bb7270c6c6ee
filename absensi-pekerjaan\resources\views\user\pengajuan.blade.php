@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card shadow-lg border-0 rounded-lg">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Riwayat Pengajuan Izin/Cuti/Sakit</h5>
                    <a href="{{ route('izin.form') }}" class="btn btn-sm btn-dark">
                        <i class="bi bi-plus-circle"></i> Buat Pengajuan Baru
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filter Form -->
                    <form action="{{ route('absen.pengajuan') }}" method="GET" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="bulan" class="form-label">Bulan</label>
                                <select class="form-select" id="bulan" name="bulan">
                                    <option value="">Semua Bulan</option>
                                    @for($i = 1; $i <= 12; $i++)
                                        <option value="{{ $i }}" {{ request('bulan') == $i ? 'selected' : '' }}>
                                            {{ \Carbon\Carbon::create(null, $i, 1)->locale('id')->monthName }}
                                        </option>
                                    @endfor
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="tahun" class="form-label">Tahun</label>
                                <select class="form-select" id="tahun" name="tahun">
                                    <option value="">Semua Tahun</option>
                                    @for($i = date('Y'); $i >= date('Y') - 5; $i--)
                                        <option value="{{ $i }}" {{ request('tahun') == $i ? 'selected' : '' }}>{{ $i }}</option>
                                    @endfor
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="jenis" class="form-label">Jenis</label>
                                <select class="form-select" id="jenis" name="jenis">
                                    <option value="">Semua Jenis</option>
                                    <option value="izin" {{ request('jenis') == 'izin' ? 'selected' : '' }}>Izin</option>
                                    <option value="sakit" {{ request('jenis') == 'sakit' ? 'selected' : '' }}>Sakit</option>
                                    <option value="cuti" {{ request('jenis') == 'cuti' ? 'selected' : '' }}>Cuti</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="status_approval" class="form-label">Status</label>
                                <select class="form-select" id="status_approval" name="status_approval">
                                    <option value="">Semua Status</option>
                                    <option value="pending" {{ request('status_approval') == 'pending' ? 'selected' : '' }}>Menunggu</option>
                                    <option value="approved" {{ request('status_approval') == 'approved' ? 'selected' : '' }}>Disetujui</option>
                                    <option value="rejected" {{ request('status_approval') == 'rejected' ? 'selected' : '' }}>Ditolak</option>
                                </select>
                            </div>
                            <div class="col-md-12 mt-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-filter"></i> Filter
                                </button>
                                <a href="{{ route('absen.pengajuan') }}" class="btn btn-secondary">
                                    <i class="bi bi-arrow-repeat"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">No</th>
                                    <th width="15%">Tanggal</th>
                                    <th width="10%">Jenis</th>
                                    <th width="10%">Durasi</th>
                                    <th width="15%">Status</th>
                                    <th width="25%">Keterangan</th>
                                    <th width="20%">Dokumen</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($pengajuanGrup as $index => $item)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                @if(isset($item->tanggal_mulai) && isset($item->tanggal_selesai))
                                                    <span class="fw-bold">{{ \Carbon\Carbon::parse($item->tanggal_mulai)->format('d/m/Y') }}</span>
                                                    <span>s/d</span>
                                                    <span class="fw-bold">{{ \Carbon\Carbon::parse($item->tanggal_selesai)->format('d/m/Y') }}</span>
                                                @else
                                                    <span class="fw-bold">{{ \Carbon\Carbon::parse($item->tanggal_awal ?? $item->tanggal)->format('d/m/Y') }}</span>
                                                    <span>s/d</span>
                                                    <span class="fw-bold">{{ \Carbon\Carbon::parse($item->tanggal_akhir ?? $item->tanggal)->format('d/m/Y') }}</span>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            @if($item->status == 'izin')
                                                <span class="badge bg-warning">Izin</span>
                                            @elseif($item->status == 'sakit')
                                                <span class="badge bg-info">Sakit</span>
                                            @elseif($item->status == 'cuti')
                                                <span class="badge bg-primary">Cuti</span>
                                            @endif
                                        </td>
                                        <td>{{ $item->jumlah_hari ?? '1' }} hari</td>
                                        <td>
                                            @if($item->status_approval == 'pending')
                                                <span class="badge bg-secondary">Menunggu</span>
                                            @elseif($item->status_approval == 'approved')
                                                <span class="badge bg-success">Disetujui</span>
                                                @if($item->approval_at)
                                                    <small class="d-block text-muted">{{ \Carbon\Carbon::parse($item->approval_at)->format('d/m/Y H:i') }}</small>
                                                @endif
                                            @elseif($item->status_approval == 'rejected')
                                                <span class="badge bg-danger">Ditolak</span>
                                                @if($item->approval_at)
                                                    <small class="d-block text-muted">{{ \Carbon\Carbon::parse($item->approval_at)->format('d/m/Y H:i') }}</small>
                                                @endif
                                            @endif

                                            @if($item->approval_note)
                                                <small class="d-block mt-1">
                                                    <i class="bi bi-info-circle text-muted"></i>
                                                    {{ $item->approval_note }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ $item->keterangan }}">
                                                {{ $item->keterangan }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-2">
                                                @if($item->status_approval == 'pending')
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#batalkanModal{{ $item->id }}">
                                                        <i class="bi bi-x-circle"></i> Batalkan
                                                    </button>

                                                    <!-- Modal Batalkan -->
                                                    <div class="modal fade" id="batalkanModal{{ $item->id }}" tabindex="-1" aria-labelledby="batalkanModalLabel{{ $item->id }}" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header bg-danger text-white">
                                                                    <h5 class="modal-title" id="batalkanModalLabel{{ $item->id }}">Batalkan Pengajuan</h5>
                                                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <p>Anda yakin ingin membatalkan pengajuan ini?</p>
                                                                    <p><strong>Catatan:</strong> Pengajuan yang sudah dibatalkan tidak dapat dikembalikan.</p>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                                                    <form action="{{ route('izin.batalkan', $item->id) }}" method="POST">
                                                                        @csrf
                                                                        @method('DELETE')
                                                                        <button type="submit" class="btn btn-danger">Batalkan Pengajuan</button>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif

                                                @if($item->tanda_tangan)
                                                    <button type="button" class="btn btn-sm btn-secondary" data-bs-toggle="modal" data-bs-target="#tandaTanganModal{{ $item->id }}">
                                                        <i class="bi bi-pen"></i> Tanda Tangan
                                                    </button>

                                                    <!-- Modal Tanda Tangan -->
                                                    <div class="modal fade" id="tandaTanganModal{{ $item->id }}" tabindex="-1" aria-labelledby="tandaTanganModalLabel{{ $item->id }}" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header bg-secondary text-white">
                                                                    <h5 class="modal-title" id="tandaTanganModalLabel{{ $item->id }}">Tanda Tangan Digital</h5>
                                                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body text-center">
                                                                    <img src="{{ asset($item->tanda_tangan) }}" alt="Tanda Tangan" class="img-fluid rounded" style="max-height: 200px;">
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif

                                                @if($item->dokumen)
                                                    @php
                                                        $extension = pathinfo($item->dokumen, PATHINFO_EXTENSION);
                                                        $isPdf = strtolower($extension) === 'pdf';
                                                    @endphp

                                                    @if($isPdf)
                                                        <a href="{{ asset('storage/' . $item->dokumen) }}" class="btn btn-sm btn-danger" target="_blank">
                                                            <i class="bi bi-file-pdf"></i> Dokumen
                                                        </a>
                                                    @else
                                                        <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#dokumenModal{{ $item->id }}">
                                                            <i class="bi bi-file-image"></i> Dokumen
                                                        </button>

                                                        <!-- Modal Dokumen -->
                                                        <div class="modal fade" id="dokumenModal{{ $item->id }}" tabindex="-1" aria-labelledby="dokumenModalLabel{{ $item->id }}" aria-hidden="true">
                                                            <div class="modal-dialog modal-lg">
                                                                <div class="modal-content">
                                                                    <div class="modal-header bg-info text-white">
                                                                        <h5 class="modal-title" id="dokumenModalLabel{{ $item->id }}">Dokumen Pendukung</h5>
                                                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                    </div>
                                                                    <div class="modal-body text-center">
                                                                        <img src="{{ asset('storage/' . $item->dokumen) }}" alt="Dokumen" class="img-fluid rounded" style="max-height: 500px;">
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <a href="{{ asset('storage/' . $item->dokumen) }}" class="btn btn-success" download>
                                                                            <i class="bi bi-download"></i> Unduh
                                                                        </a>
                                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                @else
                                                    <span class="text-muted">Tidak ada dokumen</span>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="bi bi-calendar-x fs-1 text-muted mb-2"></i>
                                                <h5>Tidak ada data pengajuan</h5>
                                                <p class="text-muted">Belum ada riwayat pengajuan izin/cuti/sakit yang tersedia</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Aktifkan semua tooltip
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
@endsection
