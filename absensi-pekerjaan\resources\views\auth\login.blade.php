@extends('layouts.app')

@section('content')
<div class="login-fullwidth">
    <div class="login-bg"></div>
    <div class="login-overlay"></div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-9 col-lg-6 col-xl-5">
                <div class="login-card">
                    <div class="text-center mb-3">
                        <div class="app-logo mb-2">
                            <i class="bi bi-calendar-check"></i>
                        </div>
                        <h3 class="fw-bold">Selamat Datang</h3>
                        <p class="text-muted small">Silakan login untuk melanjutkan</p>
                    </div>

                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show py-2">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show py-2">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show py-2">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <ul class="mb-0 ps-3 small">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('login') }}">
                        @csrf
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                <input type="email" name="email" id="email" class="form-control @error('email') is-invalid @enderror" placeholder="Masukkan email Anda" value="{{ old('email') }}" required autofocus>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                <input type="password" name="password" id="password" class="form-control @error('password') is-invalid @enderror" placeholder="Masukkan password Anda" required>
                                <button type="button" class="input-group-text toggle-password" tabindex="-1">
                                    <i class="bi bi-eye"></i>
                                </button>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="remember" id="remember" {{ old('remember') ? 'checked' : '' }}>
                                <label class="form-check-label small" for="remember">
                                    Ingat saya
                                </label>
                            </div>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-box-arrow-in-right me-2"></i> Login
                            </button>
                        </div>

                        <div class="text-center mb-3">
                            <p class="mb-2 small">Belum punya akun? <a href="{{ route('register') }}" class="fw-bold">Register sekarang!</a></p>
                            <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-arrow-left me-1"></i> Kembali ke halaman utama
                            </a>
                        </div>

                        <hr class="my-3">

                        <div class="login-features">
                            <p class="text-center small fw-bold mb-2">Fitur Aplikasi</p>
                            <div class="row g-2">
                                <div class="col-4">
                                    <div class="feature-item">
                                        <div class="feature-icon">
                                            <i class="bi bi-camera"></i>
                                        </div>
                                        <div class="feature-text">Foto</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="feature-item">
                                        <div class="feature-icon">
                                            <i class="bi bi-pen"></i>
                                        </div>
                                        <div class="feature-text">TTD</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="feature-item">
                                        <div class="feature-icon">
                                            <i class="bi bi-graph-up"></i>
                                        </div>
                                        <div class="feature-text">Laporan</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Reset navbar styles for login page */
.navbar {
    display: none !important;
}

body, html {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    width: 100%;
    height: 100%;
}

.container {
    position: relative;
    z-index: 10;
}

/* Full width background */
.login-fullwidth {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    min-height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
}

.login-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    filter: blur(0px);
    z-index: 1;
}

.login-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(46, 89, 217, 0.9) 0%, rgba(28, 200, 138, 0.8) 100%);
    z-index: 2;
}

/* Login Card */
.login-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
    position: relative;
    z-index: 10;
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    max-width: 400px;
    margin: 0 auto;
}

.login-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.18);
}

/* App Logo */
.app-logo {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 1.75rem;
    color: white;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.app-logo::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    margin-bottom: 0.4rem;
}

.form-control {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 10px 12px;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.15);
}

.input-group-text {
    background-color: #f8f9fc;
    border: 1px solid #e2e8f0;
    color: #6c757d;
    font-size: 0.9rem;
}

.input-group .form-control {
    border-radius: 0 6px 6px 0;
}

.input-group .input-group-text:first-child {
    border-radius: 6px 0 0 6px;
}

.toggle-password {
    cursor: pointer;
    border-radius: 0 6px 6px 0;
}

.toggle-password:hover {
    background-color: #e9ecef;
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border: none;
    border-radius: 6px;
    padding: 10px 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
    z-index: -1;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3a5cbe 0%, #1e429e 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(46, 89, 217, 0.25);
}

/* Features */
.login-features {
    position: relative;
    z-index: 10;
}

.feature-item {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 6px;
    padding: 8px;
    display: flex;
    align-items: center;
    flex-direction: column;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.4);
    text-align: center;
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.7);
}

.feature-icon {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
    font-size: 0.85rem;
    color: white;
    flex-shrink: 0;
}

.feature-text {
    font-weight: 600;
    color: #333;
    font-size: 0.75rem;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
    .login-card {
        padding: 30px 20px;
    }

    .login-fullwidth {
        padding: 20px 0;
    }

    .feature-item {
        margin-bottom: 15px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.querySelector('.toggle-password');
    const passwordInput = document.querySelector('#password');

    if (togglePassword && passwordInput) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle icon
            const icon = this.querySelector('i');
            icon.classList.toggle('bi-eye');
            icon.classList.toggle('bi-eye-slash');
        });
    }

    // Handle form submission with CSRF token refresh
    const loginForm = document.querySelector('form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            // Get fresh CSRF token before submit
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            const csrfInput = document.querySelector('input[name="_token"]');

            if (csrfToken && csrfInput) {
                csrfInput.value = csrfToken.getAttribute('content');
            }
        });
    }

    // Auto-refresh CSRF token every 2 minutes to prevent expiration
    setInterval(function() {
        fetch('/login', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache'
            }
        })
        .then(response => {
            if (response.ok) {
                return response.text();
            }
            throw new Error('Network response was not ok');
        })
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newToken = doc.querySelector('input[name="_token"]');
            const currentToken = document.querySelector('input[name="_token"]');
            const metaToken = document.querySelector('meta[name="csrf-token"]');

            if (newToken && currentToken) {
                currentToken.value = newToken.value;
                if (metaToken) {
                    metaToken.setAttribute('content', newToken.value);
                }
                console.log('CSRF token refreshed successfully');
            }
        })
        .catch(error => {
            console.log('Token refresh failed:', error);
            // If token refresh fails, show warning to user
            const existingWarning = document.querySelector('.token-warning');
            if (!existingWarning) {
                const warningDiv = document.createElement('div');
                warningDiv.className = 'alert alert-warning alert-dismissible fade show py-2 token-warning';
                warningDiv.innerHTML = `
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    Koneksi tidak stabil. Jika mengalami masalah login, silakan refresh halaman.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;

                const form = document.querySelector('form');
                if (form) {
                    form.parentNode.insertBefore(warningDiv, form);
                }
            }
        });
    }, 120000); // 2 minutes

    // Check if there's a 419 error and show user-friendly message
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('error') === '419') {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-dismissible fade show py-2';
        alertDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            Sesi Anda telah berakhir. Silakan login kembali.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        const form = document.querySelector('form');
        if (form) {
            form.parentNode.insertBefore(alertDiv, form);
        }

        // Remove error parameter from URL
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
    }
});
</script>
@endsection
