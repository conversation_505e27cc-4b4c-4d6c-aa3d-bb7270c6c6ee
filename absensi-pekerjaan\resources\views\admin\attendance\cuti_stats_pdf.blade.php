<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitor <PERSON><PERSON> {{ $tahun }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .company-address {
            font-size: 11px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .report-period {
            font-size: 12px;
            color: #666;
        }
        
        .info-section {
            margin-bottom: 20px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        
        .info-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .info-grid {
            display: table;
            width: 100%;
        }
        
        .info-row {
            display: table-row;
        }
        
        .info-cell {
            display: table-cell;
            padding: 3px 10px 3px 0;
            vertical-align: top;
        }
        
        .stats-section {
            margin-bottom: 25px;
        }
        
        .stats-grid {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }
        
        .stats-row {
            display: table-row;
        }
        
        .stats-cell {
            display: table-cell;
            width: 25%;
            padding: 15px;
            text-align: center;
            border: 1px solid #ddd;
            background-color: #f8f9fa;
        }
        
        .stats-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 11px;
            color: #666;
            text-transform: uppercase;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .table th {
            background-color: #343a40;
            color: white;
            font-weight: bold;
            text-align: center;
        }
        
        .table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }
        
        .badge-primary {
            background-color: #007bff;
        }
        
        .badge-success {
            background-color: #28a745;
        }
        
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .badge-danger {
            background-color: #dc3545;
        }
        
        .progress {
            width: 100%;
            height: 15px;
            background-color: #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            color: white;
            text-align: center;
            font-size: 10px;
            line-height: 15px;
        }
        
        .progress-bar-success {
            background-color: #28a745;
        }
        
        .progress-bar-warning {
            background-color: #ffc107;
        }
        
        .progress-bar-danger {
            background-color: #dc3545;
        }
        
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 11px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .employee-info {
            display: flex;
            align-items: center;
        }
        
        .employee-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .employee-position {
            font-size: 10px;
            color: #666;
        }
        
        .text-success {
            color: #28a745 !important;
            font-weight: bold;
        }
        
        .text-danger {
            color: #dc3545 !important;
            font-weight: bold;
        }
        
        .text-info {
            color: #17a2b8 !important;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ $namaPerusahaan }}</div>
        <div class="company-address">{{ $alamatPerusahaan }}</div>
        <div class="report-title">MONITOR CUTI KARYAWAN</div>
        <div class="report-period">Tahun {{ $tahun }} | {{ $filterInfo }}</div>
    </div>

    <!-- Info Section -->
    <div class="info-section">
        <div class="info-title">📋 Kebijakan Cuti Tahunan</div>
        <div class="info-grid">
            <div class="info-row">
                <div class="info-cell"><strong>Jatah Cuti:</strong></div>
                <div class="info-cell">12 hari per tahun untuk setiap karyawan</div>
            </div>
            <div class="info-row">
                <div class="info-cell"><strong>Tidak Dipotong Gaji:</strong></div>
                <div class="info-cell">Cuti yang disetujui tidak mengurangi gaji</div>
            </div>
            <div class="info-row">
                <div class="info-cell"><strong>Approval Required:</strong></div>
                <div class="info-cell">Semua cuti harus mendapat persetujuan admin</div>
            </div>
            <div class="info-row">
                <div class="info-cell"><strong>Carry Over:</strong></div>
                <div class="info-cell">Sisa cuti tidak dapat dibawa ke tahun berikutnya</div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-section">
        <div class="stats-grid">
            <div class="stats-row">
                <div class="stats-cell">
                    <div class="stats-number">{{ $totalKaryawan }}</div>
                    <div class="stats-label">Total Karyawan</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-number">{{ $totalCutiTerpakai }}</div>
                    <div class="stats-label">Cuti Terpakai</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-number">{{ $totalSisaCuti }}</div>
                    <div class="stats-label">Sisa Cuti</div>
                </div>
                <div class="stats-cell">
                    <div class="stats-number">{{ $karyawanMelebihi }}</div>
                    <div class="stats-label">Melebihi Batas</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <table class="table">
        <thead>
            <tr>
                <th width="5%">No</th>
                <th width="25%">Karyawan</th>
                <th width="12%">Jatah Cuti</th>
                <th width="15%">Cuti Terpakai</th>
                <th width="13%">Sisa Cuti</th>
                <th width="15%">Persentase</th>
                <th width="15%">Status</th>
            </tr>
        </thead>
        <tbody>
            @forelse($cutiStats as $index => $stat)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>
                        <div class="employee-name">{{ $stat['user']->name }}</div>
                        <div class="employee-position">{{ $stat['user']->jabatan ?? 'Karyawan' }}</div>
                    </td>
                    <td class="text-center">
                        <span class="badge badge-primary">12 hari</span>
                    </td>
                    <td class="text-center">
                        <span class="{{ $stat['cuti_terpakai'] > 12 ? 'text-danger' : 'text-success' }}">
                            {{ $stat['cuti_terpakai'] }} hari
                        </span>
                    </td>
                    <td class="text-center">
                        <span class="{{ $stat['sisa_cuti'] < 0 ? 'text-danger' : 'text-info' }}">
                            {{ $stat['sisa_cuti'] }} hari
                        </span>
                    </td>
                    <td class="text-center">
                        @php
                            $persentase = ($stat['cuti_terpakai'] / 12) * 100;
                            $persentase = min($persentase, 100);
                            $progressClass = $persentase > 100 ? 'progress-bar-danger' : ($persentase > 80 ? 'progress-bar-warning' : 'progress-bar-success');
                        @endphp
                        <div class="progress">
                            <div class="progress-bar {{ $progressClass }}" style="width: {{ $persentase }}%">
                                {{ round($persentase) }}%
                            </div>
                        </div>
                    </td>
                    <td class="text-center">
                        @if($stat['status'] == 'exceeded')
                            <span class="badge badge-danger">Melebihi Batas</span>
                        @elseif($stat['sisa_cuti'] <= 2)
                            <span class="badge badge-warning">Hampir Habis</span>
                        @else
                            <span class="badge badge-success">Normal</span>
                        @endif
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7" class="text-center">Tidak ada data karyawan</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <!-- Footer -->
    <div class="footer">
        <div>Dicetak pada: {{ date('d F Y, H:i') }} WIB</div>
        <div>Sistem Absensi {{ $namaPerusahaan }}</div>
    </div>
</body>
</html>
