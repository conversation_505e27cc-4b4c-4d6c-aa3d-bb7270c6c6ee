<?php

namespace App\Http\Controllers;

use App\Models\Absensi;
use App\Models\Setting;
use Illuminate\Http\Request;

class AbsensiController extends Controller
{
    public function formAbsen()
    {
        // Cek apakah sudah absen masuk hari ini
        $today = date('Y-m-d');
        $absenHariIni = Absensi::where('user_id', auth()->id())
            ->whereDate('tanggal', $today)
            ->where('status', 'hadir')
            ->first();

        if ($absenHariIni && $absenHariIni->jam_masuk && !$absenHariIni->jam_keluar) {
            // Sudah absen masuk, belum absen keluar - cek validasi waktu keluar
            return $this->formAbsenKeluar();
        } elseif ($absenHariIni && $absenHariIni->jam_masuk && $absenHariIni->jam_keluar) {
            // Sudah absen masuk dan keluar
            return redirect('/dashboard')->with('info', 'Anda sudah melakukan absensi lengkap hari ini');
        } else {
            // Belum absen sama sekali - cek validasi waktu masuk
            return $this->formAbsenMasuk();
        }
    }

    public function formAbsenMasuk()
    {
        // Validasi waktu absen masuk
        $jamMasukRaw = Setting::getValue('jam_masuk', '08:00:00');
        $jamMasuk = $this->normalizeTimeFormat($jamMasukRaw);
        $batasAbsenMasuk = Setting::getValue('batas_absen_masuk', '10:00:00'); // Batas maksimal absen masuk
        $currentTime = now()->format('H:i:s');

        // Cek apakah masih dalam waktu yang diizinkan untuk absen masuk
        if ($currentTime > $batasAbsenMasuk) {
            $batasFormatted = \Carbon\Carbon::createFromFormat('H:i:s', $batasAbsenMasuk)->format('H:i');
            return redirect('/dashboard')->with('error', "Waktu absen masuk sudah berakhir. Batas absen masuk adalah pukul {$batasFormatted}. Waktu sekarang: " . now()->format('H:i') . ". Silakan hubungi admin untuk absen manual.");
        }

        return view('user.absen_masuk');
    }

    public function formAbsenKeluar()
    {
        $today = date('Y-m-d');
        $absenHariIni = Absensi::where('user_id', auth()->id())
            ->whereDate('tanggal', $today)
            ->where('status', 'hadir')
            ->first();

        if (!$absenHariIni) {
            return redirect('/absen/masuk')->with('error', 'Anda harus absen masuk terlebih dahulu');
        }

        if (!$absenHariIni->jam_masuk) {
            return redirect('/absen/masuk')->with('error', 'Anda harus absen masuk terlebih dahulu');
        }

        if ($absenHariIni->jam_keluar) {
            return redirect('/dashboard')->with('info', 'Anda sudah melakukan absensi keluar hari ini');
        }

        // Validasi waktu absen keluar
        $jamPulangRaw = Setting::getValue('jam_pulang', '17:00:00');
        $jamPulang = $this->normalizeTimeFormat($jamPulangRaw);
        $currentTime = now()->format('H:i:s');
        $minimumWorkHours = (int)Setting::getValue('minimum_jam_kerja', '8');

        // Cek apakah sudah waktunya pulang ATAU sudah kerja minimum jam
        $canCheckOut = false;
        $reason = '';

        // Option 1: Sudah lewat jam pulang
        if ($currentTime >= $jamPulang) {
            $canCheckOut = true;
        }
        // Option 2: Sudah kerja minimum jam (jika ada absen masuk)
        elseif ($absenHariIni && $absenHariIni->jam_masuk) {
            $jamMasuk = \Carbon\Carbon::parse($absenHariIni->jam_masuk);
            $jamSekarang = \Carbon\Carbon::now();
            $jamKerja = $jamSekarang->diffInHours($jamMasuk);

            if ($jamKerja >= $minimumWorkHours) {
                $canCheckOut = true;
                $reason = " (Sudah kerja {$jamKerja} jam)";
            }
        }

        if (!$canCheckOut) {
            $jamPulangFormatted = \Carbon\Carbon::createFromFormat('H:i:s', $jamPulang)->format('H:i');
            $workHoursLeft = '';

            if ($absenHariIni && $absenHariIni->jam_masuk) {
                try {
                    $jamMasuk = \Carbon\Carbon::parse($absenHariIni->jam_masuk);
                    $jamSekarang = \Carbon\Carbon::now();

                    // Hitung selisih dalam menit, lalu konversi ke jam
                    $totalMenit = $jamSekarang->diffInMinutes($jamMasuk);
                    $jamKerja = floor($totalMenit / 60);
                    $menitKerja = $totalMenit % 60;

                    // Konversi minimum jam kerja ke menit untuk perbandingan yang akurat
                    $minimumMenit = $minimumWorkHours * 60;
                    $sisaMenit = $minimumMenit - $totalMenit;
                    $sisaJam = floor($sisaMenit / 60);
                    $sisaMenitSaja = $sisaMenit % 60;

                    if ($sisaJam > 0 && $sisaMenitSaja > 0) {
                        $workHoursLeft = " atau kerja {$sisaJam} jam {$sisaMenitSaja} menit lagi";
                    } elseif ($sisaJam > 0) {
                        $workHoursLeft = " atau kerja {$sisaJam} jam lagi";
                    } else {
                        $workHoursLeft = " atau kerja {$sisaMenitSaja} menit lagi";
                    }
                } catch (\Exception $e) {
                    $workHoursLeft = " atau tunggu jam pulang";
                }
            }

            return redirect('/dashboard')->with('error', "Anda belum bisa absen keluar. Tunggu hingga pukul {$jamPulangFormatted}{$workHoursLeft}. Waktu sekarang: " . now()->format('H:i'));
        }

        return view('user.absen_keluar', compact('absenHariIni'));
    }

    public function submitAbsenMasuk(Request $request)
    {
        try {
            // Validasi input
            $request->validate([
                'tanggal' => 'required|date',
                'jam_masuk' => 'required',
                'lokasi_masuk' => 'required',
                'foto_masuk' => 'required',
                'tanda_tangan' => 'required',
                'keterangan' => 'nullable|string',
            ]);

            // Cek apakah sudah absen masuk hari ini
            $today = date('Y-m-d');
            $absenHariIni = Absensi::where('user_id', auth()->id())
                ->whereDate('tanggal', $today)
                ->where('status', 'hadir')
                ->first();

            if ($absenHariIni) {
                return redirect('/dashboard')->with('error', 'Anda sudah melakukan absensi masuk hari ini');
            }

            // Validasi waktu absen masuk
            $batasAbsenMasuk = Setting::getValue('batas_absen_masuk', '10:00:00');
            $currentTime = now()->format('H:i:s');

            if ($currentTime > $batasAbsenMasuk) {
                $batasFormatted = \Carbon\Carbon::createFromFormat('H:i:s', $batasAbsenMasuk)->format('H:i');
                return redirect('/dashboard')->with('error', "Waktu absen masuk sudah berakhir. Batas absen masuk adalah pukul {$batasFormatted}. Waktu sekarang: " . now()->format('H:i') . ". Silakan hubungi admin untuk absen manual.");
            }

            // Cek keterlambatan berdasarkan pengaturan jam masuk
            $jamMasuk = Setting::getValue('jam_masuk', '08:00:00');
            $toleransiKeterlambatan = (int)Setting::getValue('toleransi_keterlambatan', '15');
            $jamMasukDateTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
            $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransiKeterlambatan);
            $jamMasukUser = \Carbon\Carbon::createFromFormat('H:i:s', $request->jam_masuk);

            $statusAbsen = 'hadir';
            $catatanAbsen = '';

            if ($jamMasukUser->gt($batasKeterlambatan)) {
                $statusAbsen = 'hadir';

                // Ambil jam masuk dari setting dan jam masuk user
                $jamMasukTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
                $jamMasukUserTime = \Carbon\Carbon::parse($request->jam_masuk);

                // Ekstrak hanya waktu (jam:menit:detik)
                $jamMasukTimeStr = $jamMasukTime->format('H:i:s');
                $jamMasukUserTimeStr = $jamMasukUserTime->format('H:i:s');

                // Buat objek Carbon dengan tanggal yang sama (hari ini)
                $today = \Carbon\Carbon::today()->format('Y-m-d');
                $carbonJamMasuk = \Carbon\Carbon::parse($today . ' ' . $jamMasukTimeStr);
                $carbonJamMasukUser = \Carbon\Carbon::parse($today . ' ' . $jamMasukUserTimeStr);

                // Hitung selisih dalam menit
                if ($carbonJamMasukUser->gt($carbonJamMasuk)) {
                    // Gunakan diffInMinutes untuk mendapatkan selisih dalam menit
                    $diffMinutes = (int)$carbonJamMasukUser->diffInMinutes($carbonJamMasuk);

                    // Konversi ke jam dan menit
                    $diffHours = (int)floor($diffMinutes / 60);
                    $remainingMinutes = (int)($diffMinutes % 60);

                    // Buat catatan dengan format jam dan menit
                    if ($diffHours > 0) {
                        $catatanAbsen = 'Terlambat ' . $diffHours . ' jam ' . $remainingMinutes . ' menit. ';
                    } else {
                        $catatanAbsen = 'Terlambat ' . $diffMinutes . ' menit. ';
                    }
                } else {
                    $catatanAbsen = 'Tepat waktu. ';
                }
            }

            // Lokasi tidak perlu divalidasi lagi

            // Simpan foto masuk
            $fotoMasukPath = null;
            if ($request->filled('foto_masuk')) {
                try {
                    $fotoMasukData = $request->foto_masuk;
                    if (strpos($fotoMasukData, 'data:image') === 0) {
                        // Ekstrak data gambar dari base64
                        $fotoMasukData = substr($fotoMasukData, strpos($fotoMasukData, ',') + 1);
                        $fotoMasukData = base64_decode($fotoMasukData);

                        // Jika data kosong atau tidak valid, lempar exception
                        if (!$fotoMasukData) {
                            throw new \Exception('Data foto tidak valid');
                        }

                        // Buat nama file yang unik
                        $fotoMasukPath = 'foto_absensi/' . time() . '_' . auth()->id() . '_masuk.jpg';

                        // Pastikan direktori ada
                        $directory = public_path('foto_absensi');
                        if (!file_exists($directory)) {
                            if (!mkdir($directory, 0777, true)) {
                                throw new \Exception('Tidak dapat membuat direktori foto_absensi. Periksa izin direktori.');
                            }
                        }

                        // Pastikan direktori dapat ditulis
                        if (!is_writable($directory)) {
                            chmod($directory, 0777);
                            if (!is_writable($directory)) {
                                throw new \Exception('Direktori foto_absensi tidak dapat ditulis. Periksa izin direktori.');
                            }
                        }

                        // Simpan file
                        if (file_put_contents(public_path($fotoMasukPath), $fotoMasukData) === false) {
                            throw new \Exception('Gagal menyimpan foto. Periksa izin direktori dan ruang disk.');
                        }

                        // Log sukses
                        \Log::info('Foto masuk berhasil disimpan: ' . $fotoMasukPath);
                    } else {
                        throw new \Exception('Format data foto tidak valid');
                    }
                } catch (\Exception $e) {
                    \Log::error('Error saat menyimpan foto masuk: ' . $e->getMessage());
                    throw new \Exception('Gagal menyimpan foto: ' . $e->getMessage());
                }
            }

            // Simpan tanda tangan
            $tandaTanganPath = null;
            if ($request->filled('tanda_tangan')) {
                $tandaTanganData = $request->tanda_tangan;
                if (strpos($tandaTanganData, 'data:image') === 0) {
                    $tandaTanganData = substr($tandaTanganData, strpos($tandaTanganData, ',') + 1);
                    $tandaTanganData = base64_decode($tandaTanganData);
                    $tandaTanganPath = 'tanda_tangan/' . time() . '_' . auth()->id() . '.png';

                    // Pastikan direktori ada
                    $directory = public_path('tanda_tangan');
                    if (!file_exists($directory)) {
                        if (!mkdir($directory, 0755, true)) {
                            throw new \Exception('Tidak dapat membuat direktori tanda_tangan. Periksa izin direktori.');
                        }
                    }

                    // Pastikan direktori dapat ditulis
                    if (!is_writable($directory)) {
                        throw new \Exception('Direktori tanda_tangan tidak dapat ditulis. Periksa izin direktori.');
                    }

                    file_put_contents(public_path($tandaTanganPath), $tandaTanganData);
                }
            }

            // Buat record absensi masuk
            Absensi::create([
                'user_id' => auth()->id(),
                'tanggal' => $request->tanggal,
                'jam_masuk' => $request->jam_masuk,
                'jam_keluar' => null, // Jam keluar akan diisi saat absen keluar
                'keterangan' => $request->keterangan,
                'status' => $statusAbsen,
                'lokasi_masuk' => $request->lokasi_masuk,
                'lokasi_keluar' => null,
                'foto_masuk' => $fotoMasukPath,
                'foto_keluar' => null,
                'tanda_tangan' => $tandaTanganPath,
                'catatan' => $catatanAbsen . 'Absen masuk berhasil pada ' . $request->jam_masuk,
            ]);

            return redirect('/dashboard')->with('success', 'Absensi masuk berhasil disimpan');
        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage())->withInput();
        }
    }

    public function submitAbsenKeluar(Request $request)
    {
        try {
            // Validasi input
            $request->validate([
                'absensi_id' => 'required|exists:absensis,id',
                'jam_keluar' => 'required',
                'lokasi_keluar' => 'required',
                'foto_keluar' => 'required',
                'keterangan' => 'nullable|string',
            ]);

            // Ambil data absensi
            $absensi = Absensi::findOrFail($request->absensi_id);

            // Pastikan absensi milik user yang login
            if ($absensi->user_id != auth()->id()) {
                return redirect('/dashboard')->with('error', 'Anda tidak memiliki akses ke data absensi ini');
            }

            // Pastikan status absensi adalah hadir
            if ($absensi->status != 'hadir') {
                return redirect('/dashboard')->with('error', 'Anda hanya dapat melakukan absen keluar untuk status kehadiran');
            }

            // Pastikan belum absen keluar
            if ($absensi->jam_keluar) {
                return redirect('/dashboard')->with('error', 'Anda sudah melakukan absensi keluar hari ini');
            }

            // Validasi waktu absen keluar
            $jamPulangRaw = Setting::getValue('jam_pulang', '17:00:00');
            $jamPulang = $this->normalizeTimeFormat($jamPulangRaw);
            $jamKeluarUser = $request->jam_keluar;
            $minimumWorkHours = (int)Setting::getValue('minimum_jam_kerja', '8');

            // Cek apakah jam keluar sesuai dengan jam pulang yang ditentukan ATAU sudah kerja minimum jam
            $canCheckOut = false;
            $reason = '';

            // Option 1: Sudah lewat jam pulang
            if ($jamKeluarUser >= $jamPulang) {
                $canCheckOut = true;
            }
            // Option 2: Sudah kerja minimum jam (jika ada absen masuk)
            elseif ($absensi->jam_masuk) {
                try {
                    $jamMasuk = \Carbon\Carbon::parse($absensi->jam_masuk);
                    $jamKeluar = \Carbon\Carbon::parse($jamKeluarUser);

                    // Hitung selisih dalam menit, lalu konversi ke jam
                    $totalMenit = $jamKeluar->diffInMinutes($jamMasuk);
                    $jamKerja = floor($totalMenit / 60);
                    $menitKerja = $totalMenit % 60;

                    // Konversi minimum jam kerja ke menit untuk perbandingan yang akurat
                    $minimumMenit = $minimumWorkHours * 60;

                    if ($totalMenit >= $minimumMenit) {
                        $canCheckOut = true;
                        if ($menitKerja > 0) {
                            $reason = " (Sudah kerja {$jamKerja} jam {$menitKerja} menit)";
                        } else {
                            $reason = " (Sudah kerja {$jamKerja} jam)";
                        }
                    }
                } catch (\Exception $e) {
                    // Jika terjadi error parsing, tidak bisa checkout
                    $reason = " (Error menghitung jam kerja)";
                }
            }

            if (!$canCheckOut) {
                $jamPulangFormatted = \Carbon\Carbon::createFromFormat('H:i:s', $jamPulang)->format('H:i');
                $workHoursLeft = '';

                if ($absensi->jam_masuk) {
                    try {
                        $jamMasuk = \Carbon\Carbon::parse($absensi->jam_masuk);
                        $jamKeluar = \Carbon\Carbon::parse($jamKeluarUser);

                        // Hitung selisih dalam menit, lalu konversi ke jam
                        $totalMenit = $jamKeluar->diffInMinutes($jamMasuk);
                        $jamKerja = floor($totalMenit / 60);
                        $menitKerja = $totalMenit % 60;

                        // Konversi minimum jam kerja ke menit untuk perbandingan yang akurat
                        $minimumMenit = $minimumWorkHours * 60;
                        $sisaMenit = $minimumMenit - $totalMenit;
                        $sisaJam = floor($sisaMenit / 60);
                        $sisaMenitSaja = $sisaMenit % 60;

                        if ($sisaJam > 0 && $sisaMenitSaja > 0) {
                            $workHoursLeft = " atau kerja {$sisaJam} jam {$sisaMenitSaja} menit lagi";
                        } elseif ($sisaJam > 0) {
                            $workHoursLeft = " atau kerja {$sisaJam} jam lagi";
                        } else {
                            $workHoursLeft = " atau kerja {$sisaMenitSaja} menit lagi";
                        }
                    } catch (\Exception $e) {
                        $workHoursLeft = " atau tunggu jam pulang";
                    }
                }

                return back()->with('error', "Anda belum bisa absen keluar. Tunggu hingga pukul {$jamPulangFormatted}{$workHoursLeft}.")->withInput();
            }

            // Lokasi tidak perlu divalidasi lagi

            // Simpan foto keluar
            $fotoKeluarPath = null;
            if ($request->filled('foto_keluar')) {
                try {
                    $fotoKeluarData = $request->foto_keluar;
                    if (strpos($fotoKeluarData, 'data:image') === 0) {
                        // Ekstrak data gambar dari base64
                        $fotoKeluarData = substr($fotoKeluarData, strpos($fotoKeluarData, ',') + 1);
                        $fotoKeluarData = base64_decode($fotoKeluarData);

                        // Jika data kosong atau tidak valid, lempar exception
                        if (!$fotoKeluarData) {
                            throw new \Exception('Data foto tidak valid');
                        }

                        // Buat nama file yang unik
                        $fotoKeluarPath = 'foto_absensi/' . time() . '_' . auth()->id() . '_keluar.jpg';

                        // Pastikan direktori ada
                        $directory = public_path('foto_absensi');
                        if (!file_exists($directory)) {
                            if (!mkdir($directory, 0777, true)) {
                                throw new \Exception('Tidak dapat membuat direktori foto_absensi. Periksa izin direktori.');
                            }
                        }

                        // Pastikan direktori dapat ditulis
                        if (!is_writable($directory)) {
                            chmod($directory, 0777);
                            if (!is_writable($directory)) {
                                throw new \Exception('Direktori foto_absensi tidak dapat ditulis. Periksa izin direktori.');
                            }
                        }

                        // Simpan file
                        if (file_put_contents(public_path($fotoKeluarPath), $fotoKeluarData) === false) {
                            throw new \Exception('Gagal menyimpan foto. Periksa izin direktori dan ruang disk.');
                        }

                        // Log sukses
                        \Log::info('Foto keluar berhasil disimpan: ' . $fotoKeluarPath);
                    } else {
                        throw new \Exception('Format data foto tidak valid');
                    }
                } catch (\Exception $e) {
                    \Log::error('Error saat menyimpan foto keluar: ' . $e->getMessage());
                    throw new \Exception('Gagal menyimpan foto: ' . $e->getMessage());
                }
            }

            // Update record absensi
            $absensi->update([
                'jam_keluar' => $request->jam_keluar,
                'lokasi_keluar' => $request->lokasi_keluar,
                'foto_keluar' => $fotoKeluarPath,
                'keterangan' => $request->keterangan ? $absensi->keterangan . ' | ' . $request->keterangan : $absensi->keterangan,
                'catatan' => $absensi->catatan . ' | Absen keluar berhasil pada ' . $request->jam_keluar,
            ]);

            // Cek dan buat record lembur jika ada
            $this->checkAndCreateOvertimeRecord($absensi);

            return redirect('/dashboard')->with('success', 'Absensi keluar berhasil disimpan');
        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage())->withInput();
        }
    }

    public function submitAbsen(Request $request)
    {
        // Metode lama, dialihkan ke metode baru
        if ($request->has('absensi_id')) {
            return $this->submitAbsenKeluar($request);
        } else {
            return $this->submitAbsenMasuk($request);
        }
    }

    public function riwayat(Request $request)
    {
        $query = Absensi::where('user_id', auth()->id())
                ->where('status', 'hadir'); // Hanya tampilkan absensi hadir

        // Filter berdasarkan bulan
        if ($request->filled('bulan')) {
            $query->whereMonth('tanggal', $request->bulan);
        }

        // Filter berdasarkan tahun
        if ($request->filled('tahun')) {
            $query->whereYear('tanggal', $request->tahun);
        }

        // Urutkan berdasarkan tanggal terbaru
        $data = $query->orderBy('tanggal', 'desc')->get();

        return view('user.riwayat', compact('data'));
    }

    public function pengajuanRiwayat(Request $request)
    {
        // Query dasar untuk pengajuan izin/cuti/sakit
        $query = Absensi::where('user_id', auth()->id())
                ->whereIn('status', ['izin', 'sakit', 'cuti']);

        // Filter berdasarkan bulan
        if ($request->filled('bulan')) {
            $query->whereMonth('tanggal', $request->bulan);
        }

        // Filter berdasarkan tahun
        if ($request->filled('tahun')) {
            $query->whereYear('tanggal', $request->tahun);
        }

        // Filter berdasarkan jenis pengajuan
        if ($request->filled('jenis')) {
            $query->where('status', $request->jenis);
        }

        // Filter berdasarkan status approval
        if ($request->filled('status_approval')) {
            $query->where('status_approval', $request->status_approval);
        }

        // Ambil semua data pengajuan
        $pengajuanAll = $query->orderBy('tanggal', 'desc')->get();

        // Kelompokkan pengajuan berdasarkan pengajuan_id
        $pengajuanGrup = [];
        $uniquePengajuanIds = [];

        foreach ($pengajuanAll as $pengajuan) {
            if (!empty($pengajuan->pengajuan_id)) {
                if (!in_array($pengajuan->pengajuan_id, $uniquePengajuanIds)) {
                    $uniquePengajuanIds[] = $pengajuan->pengajuan_id;
                    $pengajuanGrup[] = $pengajuan;
                }
            } else {
                // Jika tidak ada pengajuan_id, gunakan ID sebagai pengganti
                if (!in_array('id_' . $pengajuan->id, $uniquePengajuanIds)) {
                    $uniquePengajuanIds[] = 'id_' . $pengajuan->id;
                    $pengajuanGrup[] = $pengajuan;
                }
            }
        }

        return view('user.pengajuan', compact('pengajuanGrup'));
    }

    // Metode QR Code telah dihapus

    public function formIzin()
    {
        return view('user.izin');
    }

    public function submitIzin(Request $request)
    {
        try {
            // Validasi input
            $request->validate([
                'status' => 'required|in:izin,sakit,cuti',
                'tanggal_mulai' => 'required|date',
                'tanggal_selesai' => 'required|date|after_or_equal:tanggal_mulai',
                'keterangan' => 'required|string',
                'tanda_tangan' => 'required',
                'dokumen' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            ]);

            // Simpan dokumen jika ada
            $dokumenPath = null;
            if ($request->hasFile('dokumen')) {
                $dokumen = $request->file('dokumen');
                $dokumenPath = $dokumen->store('dokumen', 'public');
            }

            // Simpan tanda tangan
            $tandaTanganPath = null;
            if ($request->filled('tanda_tangan')) {
                $tandaTanganData = $request->tanda_tangan;
                if (strpos($tandaTanganData, 'data:image') === 0) {
                    $tandaTanganData = substr($tandaTanganData, strpos($tandaTanganData, ',') + 1);
                    $tandaTanganData = base64_decode($tandaTanganData);
                    $tandaTanganPath = 'tanda_tangan/' . time() . '_' . auth()->id() . '.png';

                    // Pastikan direktori ada
                    $directory = public_path('tanda_tangan');
                    if (!file_exists($directory)) {
                        if (!mkdir($directory, 0755, true)) {
                            throw new \Exception('Tidak dapat membuat direktori tanda_tangan. Periksa izin direktori.');
                        }
                    }

                    // Pastikan direktori dapat ditulis
                    if (!is_writable($directory)) {
                        throw new \Exception('Direktori tanda_tangan tidak dapat ditulis. Periksa izin direktori.');
                    }

                    file_put_contents(public_path($tandaTanganPath), $tandaTanganData);
                }
            }

            // Hitung jumlah hari
            $tanggalMulai = \Carbon\Carbon::parse($request->tanggal_mulai);
            $tanggalSelesai = \Carbon\Carbon::parse($request->tanggal_selesai);
            $jumlahHari = $tanggalMulai->diffInDays($tanggalSelesai) + 1;

            // Buat ID pengajuan unik
            $pengajuanId = uniqid('pengajuan_');

            // Buat catatan
            $catatan = "Pengajuan {$request->status} dari {$request->tanggal_mulai} sampai {$request->tanggal_selesai} ({$jumlahHari} hari)";

            // Buat satu record absensi untuk seluruh rentang tanggal
            Absensi::create([
                'user_id' => auth()->id(),
                'tanggal' => $tanggalMulai->format('Y-m-d'), // Tanggal pertama sebagai tanggal utama
                'tanggal_mulai' => $tanggalMulai->format('Y-m-d'),
                'tanggal_selesai' => $tanggalSelesai->format('Y-m-d'),
                'jumlah_hari' => $jumlahHari,
                'pengajuan_id' => $pengajuanId,
                'jam_masuk' => null, // Tidak perlu jam untuk izin/cuti/sakit
                'jam_keluar' => null, // Tidak perlu jam untuk izin/cuti/sakit
                'keterangan' => $request->keterangan,
                'status' => $request->status,
                'status_approval' => 'pending', // Default status approval
                'lokasi_masuk' => 'Pengajuan online',
                'lokasi_keluar' => 'Pengajuan online',
                'foto_masuk' => null,
                'foto_keluar' => null,
                'tanda_tangan' => $tandaTanganPath,
                'dokumen' => $dokumenPath, // Simpan path dokumen
                'catatan' => $catatan,
            ]);

            return redirect('/dashboard')->with('success', "Pengajuan {$request->status} berhasil disimpan dan menunggu persetujuan admin");
        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Membatalkan pengajuan izin/cuti/sakit
     */
    public function batalkanPengajuan($id)
    {
        try {
            // Cari pengajuan berdasarkan ID
            $pengajuan = Absensi::findOrFail($id);

            // Pastikan pengajuan milik user yang sedang login
            if ($pengajuan->user_id != auth()->id()) {
                return redirect()->route('absen.pengajuan')->with('error', 'Anda tidak memiliki akses untuk membatalkan pengajuan ini');
            }

            // Pastikan pengajuan masih dalam status pending
            if ($pengajuan->status_approval != 'pending') {
                return redirect()->route('absen.pengajuan')->with('error', 'Hanya pengajuan dengan status menunggu yang dapat dibatalkan');
            }

            // Jika ada pengajuan_id, hapus semua pengajuan dengan pengajuan_id yang sama
            if (!empty($pengajuan->pengajuan_id)) {
                Absensi::where('pengajuan_id', $pengajuan->pengajuan_id)->delete();
            } else {
                // Jika tidak ada pengajuan_id, hapus pengajuan berdasarkan ID
                $pengajuan->delete();
            }

            return redirect()->route('absen.pengajuan')->with('success', 'Pengajuan berhasil dibatalkan');
        } catch (\Exception $e) {
            return redirect()->route('absen.pengajuan')->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Cek dan buat record lembur jika ada
     */
    private function checkAndCreateOvertimeRecord($absensi)
    {
        try {
            // Cek apakah sudah ada record lembur untuk absensi ini
            $existingOvertime = \App\Models\OvertimeRecord::where('absensi_id', $absensi->id)->first();
            if ($existingOvertime) {
                return; // Skip jika sudah ada
            }

            // Hitung lembur
            $overtimeData = \App\Models\OvertimeRecord::calculateOvertimeFromAbsensi($absensi);

            if ($overtimeData && $overtimeData['jam_lembur'] > 0) {
                \App\Models\OvertimeRecord::create([
                    'user_id' => $absensi->user_id,
                    'absensi_id' => $absensi->id,
                    'tanggal' => $absensi->tanggal,
                    'jam_pulang_normal' => $overtimeData['jam_pulang_normal'],
                    'jam_keluar_aktual' => $overtimeData['jam_keluar_aktual'],
                    'jam_lembur' => $overtimeData['jam_lembur'],
                    'upah_per_jam' => $overtimeData['upah_per_jam'],
                    'rate_lembur' => $overtimeData['rate_lembur'],
                    'upah_lembur_per_jam' => $overtimeData['upah_lembur_per_jam'],
                    'total_upah_lembur' => $overtimeData['total_upah_lembur'],
                    'keterangan' => 'Lembur dari absensi keluar',
                    'status' => 'pending'
                ]);
            }
        } catch (\Exception $e) {
            // Log error tapi jangan stop proses absensi
            \Log::error('Error creating overtime record: ' . $e->getMessage());
        }
    }

    /**
     * Normalisasi format waktu untuk menghindari error trailing data
     */
    private function normalizeTimeFormat($time)
    {
        try {
            // Hapus karakter yang tidak diperlukan dan normalisasi
            $time = trim($time);

            // Jika format salah (seperti 18:00:00:00), ambil hanya 3 bagian pertama
            $parts = explode(':', $time);
            if (count($parts) > 3) {
                $time = $parts[0] . ':' . $parts[1] . ':' . $parts[2];
            }

            // Validasi dengan Carbon
            $carbonTime = \Carbon\Carbon::createFromFormat('H:i:s', $time);
            return $carbonTime->format('H:i:s');
        } catch (\Exception $e) {
            // Jika gagal, return format default
            return '08:00:00';
        }
    }

    /**
     * Pengajuan absen manual oleh user
     */
    public function requestManualAttendance(Request $request)
    {
        try {
            $request->validate([
                'tanggal' => 'required|date',
                'jenis_absen' => 'required|in:masuk,keluar,masuk_keluar',
                'jam_masuk' => 'nullable|string',
                'jam_keluar' => 'nullable|string',
                'alasan' => 'required|string|max:500',
            ], [
                'tanggal.required' => 'Tanggal absen harus diisi.',
                'tanggal.date' => 'Format tanggal tidak valid.',
                'jenis_absen.required' => 'Jenis absen harus dipilih.',
                'jenis_absen.in' => 'Jenis absen tidak valid.',
                'alasan.required' => 'Alasan pengajuan harus diisi.',
                'alasan.max' => 'Alasan pengajuan maksimal 500 karakter.',
            ]);

            // Validasi format jam jika diisi
            if ($request->filled('jam_masuk')) {
                if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $request->jam_masuk)) {
                    return redirect()->back()->with('error', 'Format jam masuk tidak valid. Gunakan format HH:MM (contoh: 08:15)')->withInput();
                }
            }

            if ($request->filled('jam_keluar')) {
                if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $request->jam_keluar)) {
                    return redirect()->back()->with('error', 'Format jam keluar tidak valid. Gunakan format HH:MM (contoh: 17:05)')->withInput();
                }
            }

            // Cek apakah sudah ada absensi untuk tanggal tersebut
            $existingAbsensi = Absensi::where('user_id', auth()->id())
                ->whereDate('tanggal', $request->tanggal)
                ->first();

            if ($existingAbsensi) {
                return redirect()->back()->with('error', 'Anda sudah memiliki absensi untuk tanggal tersebut.');
            }

            // Cek apakah sudah ada pengajuan absen manual untuk tanggal tersebut
            $existingRequest = \App\Models\ManualAttendanceRequest::where('user_id', auth()->id())
                ->whereDate('tanggal', $request->tanggal)
                ->where('status', 'pending')
                ->first();

            if ($existingRequest) {
                return redirect()->back()->with('error', 'Anda sudah memiliki pengajuan absen manual yang sedang diproses untuk tanggal tersebut.');
            }

            // Buat pengajuan absen manual
            \App\Models\ManualAttendanceRequest::create([
                'user_id' => auth()->id(),
                'tanggal' => $request->tanggal,
                'jenis_absen' => $request->jenis_absen,
                'jam_masuk' => $request->jam_masuk,
                'jam_keluar' => $request->jam_keluar,
                'alasan' => $request->alasan,
                'status' => 'pending',
            ]);

            return redirect()->back()->with('success', 'Pengajuan absen manual berhasil dikirim. Silakan tunggu persetujuan admin.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Error pengajuan absen manual: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }
}
