<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('absensis', function (Blueprint $table) {
            $table->enum('status', ['hadir', 'izin', 'sakit', 'cuti', 'alpha'])->default('hadir')->after('keterangan');
            $table->string('lokasi_masuk')->nullable()->after('status');
            $table->string('lokasi_keluar')->nullable()->after('lokasi_masuk');
            $table->string('foto_masuk')->nullable()->after('lokasi_keluar');
            $table->string('foto_keluar')->nullable()->after('foto_masuk');
            $table->text('tanda_tangan')->nullable()->after('foto_keluar');
            $table->text('catatan')->nullable()->after('tanda_tangan');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('absensis', function (Blueprint $table) {
            $table->dropColumn([
                'status',
                'lokasi_masuk',
                'lokasi_keluar',
                'foto_masuk',
                'foto_keluar',
                'tanda_tangan',
                'catatan'
            ]);
        });
    }
};
