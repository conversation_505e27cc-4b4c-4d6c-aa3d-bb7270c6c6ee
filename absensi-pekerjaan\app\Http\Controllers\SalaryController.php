<?php

namespace App\Http\Controllers;

use App\Models\MonthlySalary;
use App\Models\OvertimeRecord;
use App\Models\SalaryDeduction;
use App\Models\Setting;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;

class SalaryController extends Controller
{
    public function index(Request $request)
    {
        $bulan = $request->get('bulan', date('n'));
        $tahun = $request->get('tahun', date('Y'));

        $salaries = MonthlySalary::where('user_id', auth()->id())
            ->when($bulan, function($query) use ($bulan) {
                return $query->where('bulan', $bulan);
            })
            ->when($tahun, function($query) use ($tahun) {
                return $query->where('tahun', $tahun);
            })
            ->orderBy('tahun', 'desc')
            ->orderBy('bulan', 'desc')
            ->paginate(10);

        return view('user.salary.index', compact('salaries', 'bulan', 'tahun'));
    }

    public function show($id)
    {
        $salary = MonthlySalary::where('user_id', auth()->id())
            ->with(['user', 'creator', 'approver'])
            ->findOrFail($id);

        // Ambil detail lembur dan potongan
        $overtimes = OvertimeRecord::where('user_id', auth()->id())
            ->byPeriod($salary->bulan, $salary->tahun)
            ->approved()
            ->get();

        $deductions = SalaryDeduction::where('user_id', auth()->id())
            ->byPeriod($salary->bulan, $salary->tahun)
            ->approved()
            ->get();

        return view('user.salary.show', compact('salary', 'overtimes', 'deductions'));
    }

    public function printPdf($id)
    {
        $salary = MonthlySalary::where('user_id', auth()->id())
            ->with(['user', 'creator', 'approver'])
            ->findOrFail($id);

        // Ambil pengaturan perusahaan
        $namaPerusahaan = Setting::getValue('nama_perusahaan', 'PT Absensi Pegawai');
        $alamatPerusahaan = Setting::getValue('alamat_perusahaan', 'Jalan Kolonel Masturi No 138');

        $pdf = PDF::loadView('user.salary.pdf.slip', compact('salary', 'namaPerusahaan', 'alamatPerusahaan'));
        $pdf->setPaper('a4', 'portrait');

        return $pdf->download('slip-gaji-' . $salary->periode . '.pdf');
    }

    public function overtime(Request $request)
    {
        $bulan = $request->get('bulan', date('n'));
        $tahun = $request->get('tahun', date('Y'));

        $overtimes = OvertimeRecord::where('user_id', auth()->id())
            ->byPeriod($bulan, $tahun)
            ->with('absensi')
            ->orderBy('tanggal', 'desc')
            ->paginate(10);

        return view('user.salary.overtime', compact('overtimes', 'bulan', 'tahun'));
    }

    public function deductions(Request $request)
    {
        $bulan = $request->get('bulan', date('n'));
        $tahun = $request->get('tahun', date('Y'));

        $deductions = SalaryDeduction::where('user_id', auth()->id())
            ->byPeriod($bulan, $tahun)
            ->with('absensi')
            ->orderBy('tanggal', 'desc')
            ->paginate(10);

        return view('user.salary.deductions', compact('deductions', 'bulan', 'tahun'));
    }
}
