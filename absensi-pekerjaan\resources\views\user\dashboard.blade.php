@extends('layouts.app')

@section('content')
@php
    try {
        $today = date('Y-m-d');
        $absenHariIni = \App\Models\Absensi::where('user_id', auth()->id())
            ->whereDate('tanggal', $today)
            ->where('status', 'hadir')
            ->first();

        // Ambil setting dengan validasi format
        $jamMasukRaw = \App\Models\Setting::getValue('jam_masuk', '08:00:00');
        $jamPulangRaw = \App\Models\Setting::getValue('jam_pulang', '17:00:00');
        $batasAbsenMasukRaw = \App\Models\Setting::getValue('batas_absen_masuk', '10:00:00');
        $minimumJamKerja = (int)\App\Models\Setting::getValue('minimum_jam_kerja', '8');

        // Validasi dan normalisasi format waktu
        $jamMasuk = normalizeTimeFormat($jamMasukRaw);
        $jamPulang = normalizeTimeFormat($jamPulangRaw);
        $batasAbsenMasuk = normalizeTimeFormat($batasAbsenMasukRaw);

        // Cek apakah masih bisa absen masuk
        $currentTime = now()->format('H:i:s');
        $canCheckIn = $currentTime <= $batasAbsenMasuk;

    } catch (\Exception $e) {
        // Jika terjadi error, set nilai default
        $absenHariIni = null;
        $jamMasuk = '08:00:00';
        $jamPulang = '17:00:00';
    }

    // Fungsi untuk normalisasi format waktu
    function normalizeTimeFormat($time) {
        try {
            // Hapus karakter yang tidak diperlukan dan normalisasi
            $time = trim($time);

            // Jika format salah (seperti 18:00:00:00), ambil hanya 3 bagian pertama
            $parts = explode(':', $time);
            if (count($parts) > 3) {
                $time = $parts[0] . ':' . $parts[1] . ':' . $parts[2];
            }

            // Validasi dengan Carbon
            $carbonTime = \Carbon\Carbon::createFromFormat('H:i:s', $time);
            return $carbonTime->format('H:i:s');
        } catch (\Exception $e) {
            // Jika gagal, return format default
            return '08:00:00';
        }
    }
@endphp
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 mb-4">
            <div class="card shadow-lg border-0 rounded-lg overflow-hidden">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-grid-1x2-fill me-2 fs-4"></i>
                        <h5 class="mb-0 fw-bold">Menu Utama</h5>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action active py-3 border-0">
                            <div class="d-flex align-items-center">
                                <div class="icon-circle bg-primary text-white me-3">
                                    <i class="bi bi-house-door"></i>
                                </div>
                                <span>Dashboard</span>
                            </div>
                        </a>
                        <a href="{{ route('profile.show') }}" class="list-group-item list-group-item-action py-3 border-0">
                            <div class="d-flex align-items-center">
                                <div class="icon-circle bg-info text-white me-3">
                                    <i class="bi bi-person"></i>
                                </div>
                                <span>Profil Saya</span>
                            </div>
                        </a>
                        @if(!$absenHariIni)
                            @if($canCheckIn)
                                <a href="{{ route('absen.masuk.form') }}" class="list-group-item list-group-item-action py-3 border-0">
                                    <div class="d-flex align-items-center">
                                        <div class="icon-circle bg-success text-white me-3">
                                            <i class="bi bi-calendar-check"></i>
                                        </div>
                                        <span>Absen Masuk</span>
                                    </div>
                                </a>
                            @else
                                <div class="list-group-item list-group-item-action py-3 border-0 disabled text-muted">
                                    <div class="d-flex align-items-center">
                                        <div class="icon-circle bg-secondary text-white me-3">
                                            <i class="bi bi-calendar-check"></i>
                                        </div>
                                        <span>Absen Masuk (Berakhir {{ \Carbon\Carbon::createFromFormat('H:i:s', $batasAbsenMasuk)->format('H:i') }})</span>
                                    </div>
                                </div>
                            @endif
                            <div class="list-group-item list-group-item-action py-3 border-0 disabled text-muted">
                                <div class="d-flex align-items-center">
                                    <div class="icon-circle bg-secondary text-white me-3">
                                        <i class="bi bi-calendar-x"></i>
                                    </div>
                                    <span>Absen Keluar</span>
                                </div>
                            </div>
                        @elseif($absenHariIni && !$absenHariIni->jam_keluar)
                            <div class="list-group-item list-group-item-action py-3 border-0 disabled text-muted">
                                <div class="d-flex align-items-center">
                                    <div class="icon-circle bg-secondary text-white me-3">
                                        <i class="bi bi-calendar-check"></i>
                                    </div>
                                    <span>Absen Masuk</span>
                                </div>
                            </div>
                            @php
                                // Cek apakah sudah waktunya pulang (jam 17:05)
                                $currentTime = now()->format('H:i:s');
                                $canCheckOut = false;
                                $checkOutReason = '';

                                // Hanya bisa absen keluar pada jam 17:05 atau setelahnya
                                if ($currentTime >= $jamPulang) {
                                    $canCheckOut = true;
                                    $checkOutReason = 'Sudah jam pulang';
                                } else {
                                    try {
                                        $jamPulangFormatted = \Carbon\Carbon::createFromFormat('H:i:s', $jamPulang)->format('H:i');
                                    } catch (\Exception $e) {
                                        $jamPulangFormatted = substr($jamPulang, 0, 5);
                                    }
                                    $checkOutReason = "Tunggu jam " . $jamPulangFormatted . " untuk absen keluar";
                                }
                            @endphp
                            @if($canCheckOut)
                                <a href="{{ route('absen.keluar.form') }}" class="list-group-item list-group-item-action py-3 border-0">
                                    <div class="d-flex align-items-center">
                                        <div class="icon-circle bg-danger text-white me-3">
                                            <i class="bi bi-calendar-x"></i>
                                        </div>
                                        <span>Absen Keluar</span>
                                    </div>
                                </a>
                            @else
                                <div class="list-group-item list-group-item-action py-3 border-0 disabled text-muted">
                                    <div class="d-flex align-items-center">
                                        <div class="icon-circle bg-secondary text-white me-3">
                                            <i class="bi bi-calendar-x"></i>
                                        </div>
                                        <span>Absen Keluar ({{ $checkOutReason }})</span>
                                    </div>
                                </div>
                            @endif
                        @else
                            <div class="list-group-item list-group-item-action py-3 border-0 disabled text-muted">
                                <div class="d-flex align-items-center">
                                    <div class="icon-circle bg-secondary text-white me-3">
                                        <i class="bi bi-calendar-check"></i>
                                    </div>
                                    <span>Absen Masuk</span>
                                </div>
                            </div>
                            <div class="list-group-item list-group-item-action py-3 border-0 disabled text-muted">
                                <div class="d-flex align-items-center">
                                    <div class="icon-circle bg-secondary text-white me-3">
                                        <i class="bi bi-calendar-x"></i>
                                    </div>
                                    <span>Absen Keluar</span>
                                </div>
                            </div>
                        @endif
                        <a href="{{ route('absen.riwayat') }}" class="list-group-item list-group-item-action py-3 border-0">
                            <div class="d-flex align-items-center">
                                <div class="icon-circle bg-info text-white me-3">
                                    <i class="bi bi-clock-history"></i>
                                </div>
                                <span>Riwayat Absensi</span>
                            </div>
                        </a>
                        <a href="{{ route('izin.form') }}" class="list-group-item list-group-item-action py-3 border-0">
                            <div class="d-flex align-items-center">
                                <div class="icon-circle bg-secondary text-white me-3">
                                    <i class="bi bi-file-earmark-text"></i>
                                </div>
                                <span>Izin/Cuti/Sakit</span>
                            </div>
                        </a>
                        @if(!$absenHariIni && !$canCheckIn)
                            <a href="#" onclick="openAbsenManualRequestModal()" class="list-group-item list-group-item-action py-3 border-0">
                                <div class="d-flex align-items-center">
                                    <div class="icon-circle bg-warning text-white me-3">
                                        <i class="bi bi-person-raised-hand"></i>
                                    </div>
                                    <span>Ajukan Absen Manual</span>
                                </div>
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Jam Digital -->
            <div class="card shadow-lg border-0 rounded-lg overflow-hidden mt-4">
                <div class="card-body text-center p-4 bg-gradient-dark text-white">
                    <div class="digital-clock-large mb-2">
                        <span id="hours">00</span>:<span id="minutes">00</span>:<span id="seconds">00</span>
                    </div>
                    <div id="date" class="fw-light"></div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Welcome Card -->
            <div class="card border-0 shadow-lg rounded-lg overflow-hidden mb-4">
                <div class="card-body p-0">
                    <div class="row g-0">
                        <div class="col-md-4 bg-gradient-primary text-white p-4 d-flex flex-column justify-content-center align-items-center">
                            <div class="avatar-circle-large mb-3">
                                <span class="initials">{{ substr(auth()->user()->name, 0, 1) }}</span>
                            </div>
                            <h4 class="fw-bold mb-1">{{ auth()->user()->name }}</h4>
                            <p class="mb-0 opacity-75">Pegawai</p>
                            <div class="mt-3">
                                <span class="badge bg-light text-primary px-3 py-2 rounded-pill">
                                    <i class="bi bi-check-circle-fill me-1"></i> Aktif
                                </span>
                            </div>
                        </div>
                        <div class="col-md-8 p-4">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h3 class="fw-bold text-gradient">Selamat Datang! 👋</h3>
                                <span class="badge bg-light text-dark px-3 py-2 rounded-pill">
                                    <i class="bi bi-calendar3 me-1"></i> {{ now()->format('d M Y') }}
                                </span>
                            </div>

                            <!-- Variabel $absenHariIni, $jamMasuk, dan $jamPulang sudah didefinisikan di atas -->

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="card h-100 border-0 bg-light">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="icon-sm bg-primary text-white me-3">
                                                    <i class="bi bi-clock"></i>
                                                </div>
                                                <h6 class="mb-0">Jam Kerja</h6>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-4 text-center">
                                                    <span class="d-block text-muted small">Masuk</span>
                                                    <span class="fw-bold">
                                                        @php
                                                            try {
                                                                echo \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk)->format('H:i');
                                                            } catch (\Exception $e) {
                                                                echo substr($jamMasuk, 0, 5);
                                                            }
                                                        @endphp
                                                    </span>
                                                </div>
                                                <div class="col-4 text-center">
                                                    <span class="d-block text-muted small">Batas</span>
                                                    <span class="fw-bold text-warning">
                                                        @php
                                                            try {
                                                                echo \Carbon\Carbon::createFromFormat('H:i:s', $batasAbsenMasuk)->format('H:i');
                                                            } catch (\Exception $e) {
                                                                echo substr($batasAbsenMasuk, 0, 5);
                                                            }
                                                        @endphp
                                                    </span>
                                                </div>
                                                <div class="col-4 text-center">
                                                    <span class="d-block text-muted small">Pulang</span>
                                                    <span class="fw-bold">
                                                        @php
                                                            try {
                                                                echo \Carbon\Carbon::createFromFormat('H:i:s', $jamPulang)->format('H:i');
                                                            } catch (\Exception $e) {
                                                                echo substr($jamPulang, 0, 5);
                                                            }
                                                        @endphp
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card h-100 border-0 bg-light">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="icon-sm bg-success text-white me-3">
                                                    <i class="bi bi-calendar-check"></i>
                                                </div>
                                                <h6 class="mb-0">Status Hari Ini</h6>
                                            </div>
                                            @if(!$absenHariIni)
                                                <div class="text-center mt-3">
                                                    <span class="badge bg-warning text-dark px-3 py-2 d-block">Belum Absen</span>
                                                </div>
                                            @elseif($absenHariIni && !$absenHariIni->jam_keluar)
                                                <div class="text-center mt-3">
                                                    <span class="badge bg-info text-white px-3 py-2 d-block">Sudah Absen Masuk</span>
                                                </div>
                                            @else
                                                <div class="text-center mt-3">
                                                    <span class="badge bg-success text-white px-3 py-2 d-block">Absensi Lengkap</span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card border-0 shadow-lg rounded-lg overflow-hidden mb-4">
                <div class="card-header bg-white py-3">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-lightning-charge-fill text-warning me-2 fs-4"></i>
                        <h5 class="mb-0 fw-bold">Aksi Cepat</h5>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        @if(!$absenHariIni)
                            <div class="col-md-6">
                                @if($canCheckIn)
                                    <a href="{{ route('absen.masuk.form') }}" class="card h-100 border-0 bg-success bg-opacity-10 text-success hover-card text-decoration-none">
                                        <div class="card-body d-flex align-items-center p-4">
                                            <div class="icon-lg bg-success text-white me-3">
                                                <i class="bi bi-box-arrow-in-right"></i>
                                            </div>
                                            <div>
                                                <h5 class="mb-1">Absen Masuk</h5>
                                                <p class="mb-0 text-muted small">Lakukan absen masuk sekarang</p>
                                            </div>
                                        </div>
                                    </a>
                                @else
                                    <a href="#" onclick="openAbsenManualRequestModal()" class="card h-100 border-0 bg-warning bg-opacity-10 text-warning hover-card text-decoration-none">
                                        <div class="card-body d-flex align-items-center p-4">
                                            <div class="icon-lg bg-warning text-white me-3">
                                                <i class="bi bi-person-raised-hand"></i>
                                            </div>
                                            <div>
                                                <h5 class="mb-1">Ajukan Absen Manual</h5>
                                                <p class="mb-0 text-muted small">Waktu absen masuk berakhir. Ajukan absen manual ke admin.</p>
                                            </div>
                                        </div>
                                    </a>
                                @endif
                            </div>
                        @elseif($absenHariIni && !$absenHariIni->jam_keluar)
                            @php
                                // Gunakan logika yang sama seperti di sidebar
                                $currentTime = now()->format('H:i:s');
                                $canCheckOut = false;
                                $checkOutMessage = '';

                                // Hanya bisa absen keluar pada jam 17:05 atau setelahnya
                                if ($currentTime >= $jamPulang) {
                                    $canCheckOut = true;
                                    $checkOutMessage = 'Lakukan absen keluar sekarang';
                                } else {
                                    try {
                                        $jamPulangFormatted = \Carbon\Carbon::createFromFormat('H:i:s', $jamPulang)->format('H:i');
                                    } catch (\Exception $e) {
                                        $jamPulangFormatted = substr($jamPulang, 0, 5);
                                    }
                                    $checkOutMessage = "Tunggu jam " . $jamPulangFormatted . " untuk absen keluar";
                                }
                            @endphp
                            @if($canCheckOut)
                                <div class="col-md-6">
                                    <a href="{{ route('absen.keluar.form') }}" class="card h-100 border-0 bg-danger bg-opacity-10 text-danger hover-card text-decoration-none">
                                        <div class="card-body d-flex align-items-center p-4">
                                            <div class="icon-lg bg-danger text-white me-3">
                                                <i class="bi bi-box-arrow-left"></i>
                                            </div>
                                            <div>
                                                <h5 class="mb-1">Absen Keluar</h5>
                                                <p class="mb-0 text-muted small">{{ $checkOutMessage }}</p>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            @else
                                <div class="col-md-6">
                                    <div class="card h-100 border-0 bg-warning bg-opacity-10">
                                        <div class="card-body d-flex align-items-center p-4">
                                            <div class="icon-lg bg-warning text-white me-3">
                                                <i class="bi bi-clock"></i>
                                            </div>
                                            <div>
                                                <h5 class="mb-1 text-warning">Menunggu Waktu Pulang</h5>
                                                <p class="mb-0 text-muted small">{{ $checkOutMessage }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @else
                            <div class="col-md-6">
                                <div class="card h-100 border-0 bg-light">
                                    <div class="card-body d-flex align-items-center p-4">
                                        <div class="icon-lg bg-success text-white me-3">
                                            <i class="bi bi-check-circle"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">Absensi Selesai</h5>
                                            <p class="mb-0 text-muted small">Anda telah melakukan absensi lengkap hari ini</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="col-md-6">
                            <a href="{{ route('izin.form') }}" class="card h-100 border-0 bg-warning bg-opacity-10 text-warning hover-card text-decoration-none">
                                <div class="card-body d-flex align-items-center p-4">
                                    <div class="icon-lg bg-warning text-white me-3">
                                        <i class="bi bi-file-earmark-text"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-1">Ajukan Izin</h5>
                                        <p class="mb-0 text-muted small">Buat pengajuan izin, cuti, atau sakit</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Absensi Stats -->
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card border-0 shadow-lg rounded-lg overflow-hidden h-100">
                        <div class="card-body p-0">
                            <div class="p-4 bg-primary bg-opacity-10">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="fw-bold mb-0">Absensi Bulan Ini</h6>
                                    <div class="icon-sm bg-primary text-white">
                                        <i class="bi bi-calendar-month"></i>
                                    </div>
                                </div>
                                <h3 class="fw-bold text-primary mb-0">
                                    @php
                                        try {
                                            $currentMonth = date('m');
                                            $currentYear = date('Y');
                                            $absenBulanIni = \App\Models\Absensi::where('user_id', auth()->id())
                                                ->where('status', 'hadir')
                                                ->whereMonth('tanggal', $currentMonth)
                                                ->whereYear('tanggal', $currentYear)
                                                ->count();
                                        } catch (\Exception $e) {
                                            $absenBulanIni = 0;
                                        }
                                    @endphp
                                    {{ $absenBulanIni }} Hari
                                </h3>
                            </div>
                            <div class="p-3 text-center">
                                <a href="{{ route('absen.riwayat') }}" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-eye me-1"></i> Lihat Detail
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card border-0 shadow-lg rounded-lg overflow-hidden h-100">
                        <div class="card-body p-0">
                            <div class="p-4 bg-warning bg-opacity-10">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="fw-bold mb-0">Izin/Cuti</h6>
                                    <div class="icon-sm bg-warning text-white">
                                        <i class="bi bi-file-earmark-text"></i>
                                    </div>
                                </div>
                                <h3 class="fw-bold text-warning mb-0">
                                    @php
                                        try {
                                            // Hitung jumlah pengajuan izin/cuti/sakit berdasarkan pengajuan_id yang unik
                                            $pengajuanList = \App\Models\Absensi::where('user_id', auth()->id())
                                                ->whereIn('status', ['izin', 'sakit', 'cuti'])
                                                ->whereMonth('tanggal', $currentMonth)
                                                ->whereYear('tanggal', $currentYear)
                                                ->get();

                                            // Kelompokkan berdasarkan pengajuan_id
                                            $uniquePengajuan = [];
                                            foreach ($pengajuanList as $pengajuan) {
                                                if (!empty($pengajuan->pengajuan_id) && !in_array($pengajuan->pengajuan_id, $uniquePengajuan)) {
                                                    $uniquePengajuan[] = $pengajuan->pengajuan_id;
                                                }
                                            }

                                            $izinCuti = count($uniquePengajuan);
                                        } catch (\Exception $e) {
                                            $izinCuti = 0;
                                        }
                                    @endphp
                                    {{ $izinCuti }} Pengajuan
                                </h3>
                            </div>
                            <div class="p-3 text-center">
                                <a href="{{ route('absen.pengajuan') }}" class="btn btn-sm btn-outline-warning">
                                    <i class="bi bi-eye me-1"></i> Lihat Detail
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card border-0 shadow-lg rounded-lg overflow-hidden h-100">
                        <div class="card-body p-0">
                            <div class="p-4 bg-success bg-opacity-10">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="fw-bold mb-0">Ketepatan Waktu</h6>
                                    <div class="icon-sm bg-success text-white">
                                        <i class="bi bi-check-circle"></i>
                                    </div>
                                </div>
                                <h3 class="fw-bold text-success mb-0">
                                    @php
                                        // Ambil pengaturan jam masuk dan toleransi keterlambatan
                                        $jamMasuk = \App\Models\Setting::getValue('jam_masuk', '08:00:00');
                                        $toleransiKeterlambatan = (int)\App\Models\Setting::getValue('toleransi_keterlambatan', '15');

                                        try {
                                            // Gunakan try-catch untuk menangani error parsing
                                            $jamMasukDateTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
                                            $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransiKeterlambatan);

                                            // Hitung jumlah absensi tepat waktu
                                            $absensiHadir = \App\Models\Absensi::where('user_id', auth()->id())
                                                ->where('status', 'hadir')
                                                ->whereMonth('tanggal', $currentMonth)
                                                ->whereYear('tanggal', $currentYear)
                                                ->get();

                                            $tepatWaktu = 0;
                                            foreach ($absensiHadir as $absen) {
                                                if ($absen->jam_masuk) {
                                                    try {
                                                        $jamMasukUser = \Carbon\Carbon::parse($absen->jam_masuk);
                                                        if (!$jamMasukUser->gt($batasKeterlambatan)) {
                                                            $tepatWaktu++;
                                                        }
                                                    } catch (\Exception $e) {
                                                        // Abaikan error parsing
                                                        continue;
                                                    }
                                                }
                                            }

                                            $persentase = $absenBulanIni > 0 ? round(($tepatWaktu / $absenBulanIni) * 100) : 0;
                                        } catch (\Exception $e) {
                                            // Jika terjadi error, set persentase ke 0
                                            $persentase = 0;
                                        }
                                    @endphp
                                    {{ $persentase }}%
                                </h3>
                            </div>
                            <div class="p-3 text-center">
                                <a href="{{ route('absen.riwayat') }}" class="btn btn-sm btn-outline-success">
                                    <i class="bi bi-eye me-1"></i> Lihat Detail
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Dashboard Styles */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    }

    .bg-gradient-dark {
        background: linear-gradient(135deg, #5a5c69 0%, #373840 100%);
    }

    .text-gradient {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Avatar Styles */
    .avatar-circle {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        text-align: center;
        border-radius: 50%;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }

    .avatar-circle-large {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        text-align: center;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }

    .initials {
        font-size: 50px;
        color: #fff;
        font-weight: bold;
    }

    /* Icon Styles */
    .icon-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .icon-sm {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .icon-lg {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }

    /* Digital Clock */
    .digital-clock {
        font-size: 1.5rem;
        font-weight: bold;
        color: #4e73df;
    }

    .digital-clock-large {
        font-size: 2.5rem;
        font-weight: bold;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .digital-clock-large span {
        display: inline-block;
    }

    /* Hover Effects */
    .hover-card {
        transition: all 0.3s ease;
    }

    .hover-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        function updateClock() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');

            // Update all hour elements
            document.querySelectorAll('[id="hours"]').forEach(el => {
                el.textContent = hours;
            });

            // Update all minute elements
            document.querySelectorAll('[id="minutes"]').forEach(el => {
                el.textContent = minutes;
            });

            // Update all second elements
            document.querySelectorAll('[id="seconds"]').forEach(el => {
                el.textContent = seconds;
            });

            // Update all date elements
            document.querySelectorAll('[id="date"]').forEach(el => {
                el.textContent = now.toLocaleDateString('id-ID', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            });
        }

        setInterval(updateClock, 1000);
        updateClock();
    });

    function openAbsenManualRequestModal() {
        const modal = new bootstrap.Modal(document.getElementById('absenManualRequestModal'));
        modal.show();
    }

    // Handle jenis absen change
    document.addEventListener('DOMContentLoaded', function() {
        const jenisAbsenSelect = document.getElementById('jenis_absen');
        const jamMasukField = document.getElementById('jam_masuk_request');
        const jamKeluarField = document.getElementById('jam_keluar_request');
        const jamMasukContainer = jamMasukField.closest('.col-md-6');
        const jamKeluarContainer = jamKeluarField.closest('.col-md-6');

        function toggleJamFields() {
            const jenisAbsen = jenisAbsenSelect.value;

            // Reset required attributes
            jamMasukField.removeAttribute('required');
            jamKeluarField.removeAttribute('required');

            // Show/hide fields based on selection
            switch(jenisAbsen) {
                case 'masuk':
                    jamMasukContainer.style.display = 'block';
                    jamKeluarContainer.style.display = 'none';
                    jamMasukField.setAttribute('required', 'required');
                    jamKeluarField.value = '';
                    break;
                case 'keluar':
                    jamMasukContainer.style.display = 'none';
                    jamKeluarContainer.style.display = 'block';
                    jamKeluarField.setAttribute('required', 'required');
                    jamMasukField.value = '';
                    break;
                case 'masuk_keluar':
                    jamMasukContainer.style.display = 'block';
                    jamKeluarContainer.style.display = 'block';
                    jamMasukField.setAttribute('required', 'required');
                    jamKeluarField.setAttribute('required', 'required');
                    break;
                default:
                    jamMasukContainer.style.display = 'block';
                    jamKeluarContainer.style.display = 'block';
                    break;
            }
        }

        if (jenisAbsenSelect) {
            jenisAbsenSelect.addEventListener('change', toggleJamFields);
            // Initialize on page load
            toggleJamFields();
        }
    });
</script>

<!-- Modal Pengajuan Absen Manual -->
<div class="modal fade" id="absenManualRequestModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" action="{{ route('user.absen.manual.request') }}">
                @csrf
                <div class="modal-header bg-warning text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-person-raised-hand me-2"></i>Ajukan Absen Manual
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Pengajuan Absen Manual</strong> - Gunakan fitur ini jika Anda tidak dapat melakukan absen otomatis karena kendala teknis atau situasi darurat.
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="tanggal_request" class="form-label">Tanggal Absen <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="tanggal_request" name="tanggal" value="{{ date('Y-m-d') }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="jenis_absen" class="form-label">Jenis Absen <span class="text-danger">*</span></label>
                            <select class="form-select" id="jenis_absen" name="jenis_absen" required>
                                <option value="">-- Pilih Jenis --</option>
                                <option value="masuk">Absen Masuk</option>
                                <option value="keluar">Absen Keluar</option>
                                <option value="masuk_keluar">Absen Masuk & Keluar</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="jam_masuk_request" class="form-label">Jam Masuk (Estimasi)</label>
                            <input type="time" class="form-control" id="jam_masuk_request" name="jam_masuk"
                                   value="{{ substr(\App\Models\Setting::getValue('jam_masuk', '08:00:00'), 0, 5) }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="jam_keluar_request" class="form-label">Jam Keluar (Estimasi)</label>
                            <input type="time" class="form-control" id="jam_keluar_request" name="jam_keluar"
                                   value="{{ substr(\App\Models\Setting::getValue('jam_pulang', '17:00:00'), 0, 5) }}">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="alasan" class="form-label">Alasan Pengajuan <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="alasan" name="alasan" rows="3" required placeholder="Jelaskan alasan mengapa Anda memerlukan absen manual (contoh: lupa absen, masalah teknis, dll)"></textarea>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <small>
                            <strong>Catatan:</strong> Pengajuan ini akan dikirim ke admin untuk disetujui.
                            Anda akan mendapat notifikasi setelah pengajuan diproses.
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Batal
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-send me-1"></i>Kirim Pengajuan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection
