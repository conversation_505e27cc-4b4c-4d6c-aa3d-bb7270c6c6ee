<div class="row">
    <div class="col-md-6">
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0"><i class="bi bi-person me-2"></i>Informasi <PERSON></h6>
            </div>
            <div class="card-body">
                <div>
                    <h6 class="mb-1">{{ $absensi->user->name }}</h6>
                    <small class="text-muted">{{ $absensi->user->jabatan ?? 'Karyawan' }}</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>Data Saat Ini</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <strong>Status:</strong> {{ ucfirst($absensi->status) }}<br>
                    <strong>Tanggal:</strong> {{ \Carbon\Carbon::parse($absensi->tanggal)->format('d/m/Y') }}<br>
                    @if($absensi->jam_masuk)
                        <strong>Jam Masuk:</strong> {{ \Carbon\Carbon::parse($absensi->jam_masuk)->format('H:i') }}<br>
                    @endif
                    @if($absensi->jam_keluar)
                        <strong>Jam Keluar:</strong> {{ \Carbon\Carbon::parse($absensi->jam_keluar)->format('H:i') }}
                    @endif
                </small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="mb-3">
            <label class="form-label fw-bold">
                <i class="bi bi-calendar3 me-2 text-primary"></i>Tanggal
            </label>
            <input type="date" name="tanggal" class="form-control"
                   value="{{ $absensi->tanggal->format('Y-m-d') }}" required>
        </div>

        <div class="mb-3">
            <label class="form-label fw-bold">
                <i class="bi bi-tag me-2 text-primary"></i>Status
            </label>
            <select name="status" class="form-select" required>
                <option value="hadir" {{ $absensi->status == 'hadir' ? 'selected' : '' }}>Hadir</option>
                <option value="alpha" {{ $absensi->status == 'alpha' ? 'selected' : '' }}>Alpha</option>
                <option value="izin" {{ $absensi->status == 'izin' ? 'selected' : '' }}>Izin</option>
                <option value="sakit" {{ $absensi->status == 'sakit' ? 'selected' : '' }}>Sakit</option>
                <option value="cuti" {{ $absensi->status == 'cuti' ? 'selected' : '' }}>Cuti</option>
            </select>
        </div>

        <div class="mb-3">
            <label class="form-label fw-bold">
                <i class="bi bi-clock me-2 text-primary"></i>Jam Masuk
            </label>
            <input type="time" name="jam_masuk" class="form-control"
                   value="{{ $absensi->jam_masuk ? \Carbon\Carbon::parse($absensi->jam_masuk)->format('H:i') : '' }}">
            <small class="form-text text-muted">Kosongkan jika tidak ada jam masuk</small>
        </div>
    </div>

    <div class="col-md-6">
        <div class="mb-3">
            <label class="form-label fw-bold">
                <i class="bi bi-clock-history me-2 text-primary"></i>Jam Keluar
            </label>
            <input type="time" name="jam_keluar" class="form-control"
                   value="{{ $absensi->jam_keluar ? \Carbon\Carbon::parse($absensi->jam_keluar)->format('H:i') : '' }}">
            <small class="form-text text-muted">Kosongkan jika belum absen keluar</small>
        </div>

        <div class="mb-3">
            <label class="form-label fw-bold">
                <i class="bi bi-chat-text me-2 text-primary"></i>Keterangan
            </label>
            <textarea name="keterangan" class="form-control" rows="4"
                      placeholder="Keterangan tambahan...">{{ $absensi->keterangan }}</textarea>
        </div>
    </div>
</div>

<div class="alert alert-warning border-0 mt-3">
    <div class="d-flex align-items-center">
        <i class="bi bi-exclamation-triangle-fill me-3"></i>
        <div>
            <strong>⚠️ PERHATIAN - Auto-Sync Potongan Gaji:</strong>
            <ul class="mb-0 mt-2">
                <li><strong>Alpha → Hadir:</strong> Potongan alpha akan <span class="text-success">DIHAPUS OTOMATIS</span></li>
                <li><strong>Hadir → Alpha:</strong> Potongan alpha akan <span class="text-danger">DIBUAT OTOMATIS</span></li>
                <li><strong>Terlambat → Hadir:</strong> Potongan terlambat akan <span class="text-success">DIHAPUS OTOMATIS</span></li>
                <li><strong>Alpha/Terlambat → Izin/Sakit/Cuti:</strong> Semua potongan akan <span class="text-success">DIHAPUS OTOMATIS</span></li>
                <li>Sistem akan memberikan notifikasi tentang perubahan potongan gaji</li>
            </ul>
        </div>
    </div>
</div>

@if(in_array($absensi->status, ['izin', 'sakit', 'cuti']))
    <div class="alert alert-info border-0 mt-3">
        <div class="d-flex align-items-center">
            <i class="bi bi-info-circle-fill me-3"></i>
            <div>
                <strong>Informasi:</strong> Data ini terkait dengan pengajuan {{ $absensi->status }}.
                <a href="{{ route('admin.approval.show', $absensi->id) }}" class="alert-link">Lihat detail pengajuan</a>
            </div>
        </div>
    </div>
@endif
