<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Cek apakah sudah ada admin
        $existingAdmin = User::where('role', 'admin')->first();

        if (!$existingAdmin) {
            User::create([
                'name' => 'Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'admin',
                'nik' => 'ADM001',
                'jabatan' => 'Administrator',
                'departemen' => 'IT',
                'no_hp' => '081234567890',
                'alamat' => 'Kantor Pusat',
                'tanggal_lahir' => '1990-01-01',
                'tanggal_bergabung' => now(),
                'gaji_pokok' => 5000000,
                'tunjangan_transport' => 500000,
                'tunjangan_makan' => 300000,
                'tunjangan_lainnya' => 200000,
                'jumlah_sp' => 0,
            ]);

            $this->command->info('Admin user created successfully!');
            $this->command->info('Email: <EMAIL>');
            $this->command->info('Password: admin123');
        } else {
            $this->command->info('Admin user already exists: ' . $existingAdmin->email);
        }

        // Buat beberapa user dummy jika belum ada
        $userCount = User::where('role', 'user')->count();

        if ($userCount < 3) {
            $users = [
                [
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'role' => 'user',
                    'nik' => 'EMP001',
                    'jabatan' => 'Staff IT',
                    'departemen' => 'IT',
                    'no_hp' => '081234567891',
                    'alamat' => 'Jakarta',
                    'tanggal_lahir' => '1995-05-15',
                    'tanggal_bergabung' => now()->subMonths(6),
                    'gaji_pokok' => 3000000,
                    'tunjangan_transport' => 500000,
                    'tunjangan_makan' => 300000,
                    'tunjangan_lainnya' => 0,
                    'jumlah_sp' => 0,
                ],
                [
                    'name' => 'Jane Smith',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'role' => 'user',
                    'nik' => 'EMP002',
                    'jabatan' => 'Staff HR',
                    'departemen' => 'HR',
                    'no_hp' => '081234567892',
                    'alamat' => 'Bandung',
                    'tanggal_lahir' => '1992-08-20',
                    'tanggal_bergabung' => now()->subMonths(12),
                    'gaji_pokok' => 3500000,
                    'tunjangan_transport' => 500000,
                    'tunjangan_makan' => 300000,
                    'tunjangan_lainnya' => 100000,
                    'jumlah_sp' => 0,
                ],
                [
                    'name' => 'Bob Wilson',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'role' => 'user',
                    'nik' => 'EMP003',
                    'jabatan' => 'Staff Finance',
                    'departemen' => 'Finance',
                    'no_hp' => '081234567893',
                    'alamat' => 'Surabaya',
                    'tanggal_lahir' => '1988-12-10',
                    'tanggal_bergabung' => now()->subMonths(18),
                    'gaji_pokok' => 4000000,
                    'tunjangan_transport' => 600000,
                    'tunjangan_makan' => 400000,
                    'tunjangan_lainnya' => 200000,
                    'jumlah_sp' => 0,
                ]
            ];

            foreach ($users as $userData) {
                $existingUser = User::where('email', $userData['email'])->first();
                if (!$existingUser) {
                    User::create($userData);
                    $this->command->info('User created: ' . $userData['name'] . ' (' . $userData['email'] . ')');
                }
            }
        } else {
            $this->command->info('Sample users already exist.');
        }
    }
}
