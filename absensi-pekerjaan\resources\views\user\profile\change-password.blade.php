@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-key me-2"></i>Ganti Password</h5>
                    <div>
                        <a href="{{ route('profile.show') }}" class="btn btn-sm btn-light">
                            <i class="bi bi-arrow-left me-1"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body p-4">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <div>{{ session('success') }}</div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <div>{{ session('error') }}</div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="bi bi-shield-check fs-3"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading">🔐 Keamanan Password</h6>
                                <p class="mb-2">Pastikan password baru Anda memenuhi kriteria berikut:</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="mb-0">
                                            <li>✅ Minimal 8 karakter</li>
                                            <li>✅ Kombinasi huruf besar dan kecil</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="mb-0">
                                            <li>✅ Mengandung angka</li>
                                            <li>✅ Karakter khusus (opsional)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form action="{{ route('profile.password.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-4">
                            <label for="current_password" class="form-label fw-bold">Password Saat Ini <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                <input type="password" class="form-control @error('current_password') is-invalid @enderror" id="current_password" name="current_password" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="current_password">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            @error('current_password')
                                <div class="text-danger small mt-1">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label fw-bold">Password Baru <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-shield-lock"></i></span>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="password">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            @error('password')
                                <div class="text-danger small mt-1">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Minimal 8 karakter, kombinasi huruf dan angka</div>

                            <div class="password-strength mt-2 d-none" id="password-strength">
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <small class="text-muted" id="password-strength-text">Kekuatan password: Sangat lemah</small>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="password_confirmation" class="form-label fw-bold">Konfirmasi Password Baru <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-check-circle"></i></span>
                                <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button" data-target="password_confirmation">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <div class="text-danger small mt-1 d-none" id="password-match-error">Password tidak cocok</div>
                        </div>

                        <div class="d-grid gap-2 mt-5">
                            <button type="submit" class="btn btn-primary btn-lg" id="submit-btn">
                                <i class="bi bi-shield-check me-1"></i> Perbarui Password
                            </button>
                            <div class="text-center mt-2">
                                <small class="text-muted">
                                    <i class="bi bi-info-circle me-1"></i>
                                    Pastikan password memenuhi semua kriteria keamanan
                                </small>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const inputField = document.getElementById(targetId);
            const icon = this.querySelector('i');

            if (inputField.type === 'password') {
                inputField.type = 'text';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            } else {
                inputField.type = 'password';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            }
        });
    });

    // Password strength meter
    const passwordInput = document.getElementById('password');
    const strengthBar = document.querySelector('.progress-bar');
    const strengthText = document.getElementById('password-strength-text');
    const strengthContainer = document.getElementById('password-strength');

    passwordInput.addEventListener('input', function() {
        const password = this.value;
        let strength = 0;
        let criteriaMet = 0;

        if (password.length > 0) {
            strengthContainer.classList.remove('d-none');

            // Length check
            if (password.length >= 8) {
                strength += 25;
                criteriaMet++;
            }

            // Character type checks
            if (password.match(/[a-z]+/)) {
                strength += 25;
                criteriaMet++;
            }
            if (password.match(/[A-Z]+/)) {
                strength += 25;
                criteriaMet++;
            }
            if (password.match(/[0-9]+/)) {
                strength += 25;
                criteriaMet++;
            }

            // Update UI
            strengthBar.style.width = strength + '%';

            if (strength < 50) {
                strengthBar.className = 'progress-bar bg-danger';
                strengthText.textContent = 'Kekuatan password: Lemah (' + criteriaMet + '/4 kriteria)';
            } else if (strength < 75) {
                strengthBar.className = 'progress-bar bg-warning';
                strengthText.textContent = 'Kekuatan password: Sedang (' + criteriaMet + '/4 kriteria)';
            } else {
                strengthBar.className = 'progress-bar bg-success';
                strengthText.textContent = 'Kekuatan password: Kuat (' + criteriaMet + '/4 kriteria)';
            }

            // Enable/disable submit button based on strength
            const submitBtn = document.getElementById('submit-btn');
            if (strength >= 75 && criteriaMet >= 3) {
                submitBtn.disabled = false;
                submitBtn.classList.remove('btn-secondary');
                submitBtn.classList.add('btn-primary');
            } else {
                submitBtn.disabled = true;
                submitBtn.classList.remove('btn-primary');
                submitBtn.classList.add('btn-secondary');
            }
        } else {
            strengthContainer.classList.add('d-none');
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = false;
            submitBtn.classList.remove('btn-secondary');
            submitBtn.classList.add('btn-primary');
        }
    });

    // Password match validation
    const confirmInput = document.getElementById('password_confirmation');
    const matchError = document.getElementById('password-match-error');

    function checkPasswordMatch() {
        const submitBtn = document.getElementById('submit-btn');

        if (confirmInput.value && passwordInput.value !== confirmInput.value) {
            matchError.classList.remove('d-none');
            confirmInput.classList.add('is-invalid');
            confirmInput.classList.remove('is-valid');
            submitBtn.disabled = true;
            submitBtn.classList.remove('btn-primary');
            submitBtn.classList.add('btn-secondary');
        } else if (confirmInput.value && passwordInput.value === confirmInput.value) {
            matchError.classList.add('d-none');
            confirmInput.classList.remove('is-invalid');
            confirmInput.classList.add('is-valid');

            // Check if password strength is also good
            const password = passwordInput.value;
            let criteriaMet = 0;
            if (password.length >= 8) criteriaMet++;
            if (password.match(/[a-z]+/)) criteriaMet++;
            if (password.match(/[A-Z]+/)) criteriaMet++;
            if (password.match(/[0-9]+/)) criteriaMet++;

            if (criteriaMet >= 3) {
                submitBtn.disabled = false;
                submitBtn.classList.remove('btn-secondary');
                submitBtn.classList.add('btn-primary');
            }
        } else {
            matchError.classList.add('d-none');
            confirmInput.classList.remove('is-invalid', 'is-valid');
        }
    }

    passwordInput.addEventListener('input', checkPasswordMatch);
    confirmInput.addEventListener('input', checkPasswordMatch);

    // Form submission with loading state
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const submitBtn = document.getElementById('submit-btn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Memproses...';
    });
});
</script>
@endsection
