<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use Illuminate\Support\Facades\Log;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $exception)
    {
        // Handle CSRF Token Mismatch (419 error)
        if ($exception instanceof \Illuminate\Session\TokenMismatchException) {
            Log::warning('CSRF token mismatch', [
                'url' => $request->url(),
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Sesi Anda telah berakhir. Silakan refresh halaman.',
                    'code' => 419
                ], 419);
            }

            // Redirect to login for auth pages, otherwise back with error
            if (in_array($request->path(), ['login', 'register'])) {
                return redirect()->route('login')->with('error', 'Sesi Anda telah berakhir. Silakan login kembali.');
            }

            return redirect()->back()->with('error', 'Sesi Anda telah berakhir. Silakan refresh halaman dan coba lagi.');
        }

        // Handle Carbon parsing errors (trailing data)
        if ($exception instanceof \Carbon\Exceptions\InvalidFormatException ||
            strpos($exception->getMessage(), 'trailing data') !== false ||
            strpos($exception->getMessage(), 'separation symbol') !== false) {

            Log::error('Date parsing error', [
                'message' => $exception->getMessage(),
                'url' => $request->url(),
                'user_id' => auth()->id()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Terjadi kesalahan format data. Silakan coba lagi.'
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Terjadi kesalahan format data. Silakan coba lagi.')
                ->withInput($request->except(['password', 'password_confirmation']));
        }

        // Handle general exceptions
        if (!config('app.debug') && !$request->expectsJson()) {
            if ($exception instanceof \Illuminate\Database\QueryException) {
                Log::error('Database error', [
                    'message' => $exception->getMessage(),
                    'url' => $request->url(),
                    'user_id' => auth()->id()
                ]);

                return redirect()->back()
                    ->with('error', 'Terjadi kesalahan database. Silakan coba lagi.')
                    ->withInput($request->except(['password', 'password_confirmation']));
            }
        }

        return parent::render($request, $exception);
    }
}
