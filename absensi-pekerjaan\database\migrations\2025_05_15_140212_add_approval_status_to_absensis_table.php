<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('absensis', function (Blueprint $table) {
            $table->enum('status_approval', ['pending', 'approved', 'rejected'])->default('pending')->after('status');
            $table->text('approval_note')->nullable()->after('status_approval');
            $table->timestamp('approval_at')->nullable()->after('approval_note');
            $table->unsignedBigInteger('approved_by')->nullable()->after('approval_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('absensis', function (Blueprint $table) {
            $table->dropColumn('status_approval');
            $table->dropColumn('approval_note');
            $table->dropColumn('approval_at');
            $table->dropColumn('approved_by');
        });
    }
};
