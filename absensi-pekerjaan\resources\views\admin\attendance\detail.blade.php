<div class="row">
    <div class="col-md-6">
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0"><i class="bi bi-person me-2"></i>Informasi <PERSON></h6>
            </div>
            <div class="card-body">
                <div>
                    <h5 class="mb-1">{{ $absensi->user->name }}</h5>
                    <p class="text-muted mb-0">{{ $absensi->user->jabatan ?? 'Karyawan' }}</p>
                    @if($absensi->user->nik)
                        <small class="text-muted">NIK: {{ $absensi->user->nik }}</small>
                    @endif
                </div>
            </div>
        </div>

        <div class="card border-0 shadow-sm">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="bi bi-clock me-2"></i>Detail <PERSON></h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <strong>Tanggal:</strong>
                        <p class="mb-2">{{ \Carbon\Carbon::parse($absensi->tanggal)->format('d/m/Y') }}</p>
                    </div>
                    <div class="col-6">
                        <strong>Hari:</strong>
                        <p class="mb-2">{{ \Carbon\Carbon::parse($absensi->tanggal)->locale('id')->dayName }}</p>
                    </div>
                </div>

                @if($absensi->jam_masuk)
                    <div class="row">
                        <div class="col-6">
                            <strong>Jam Masuk:</strong>
                            <p class="mb-2">{{ \Carbon\Carbon::parse($absensi->jam_masuk)->format('H:i') }}</p>
                        </div>
                        @if($absensi->jam_keluar)
                            <div class="col-6">
                                <strong>Jam Keluar:</strong>
                                <p class="mb-2">{{ \Carbon\Carbon::parse($absensi->jam_keluar)->format('H:i') }}</p>
                            </div>
                        @endif
                    </div>

                    @if($absensi->jam_keluar)
                        @php
                            $jamMasuk = \Carbon\Carbon::parse($absensi->jam_masuk);
                            $jamKeluar = \Carbon\Carbon::parse($absensi->jam_keluar);
                            $totalJam = $jamKeluar->diffInHours($jamMasuk);
                            $totalMenit = $jamKeluar->diffInMinutes($jamMasuk) % 60;
                        @endphp
                        <div class="row">
                            <div class="col-12">
                                <strong>Total Jam Kerja:</strong>
                                <p class="mb-2">{{ $totalJam }} jam {{ $totalMenit }} menit</p>
                            </div>
                        </div>
                    @endif
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0"><i class="bi bi-check-circle me-2"></i>Status & Keterangan</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Status:</strong>
                    <div class="mt-1">
                        @php
                            $statusColors = [
                                'hadir' => 'success',
                                'alpha' => 'danger',
                                'izin' => 'info',
                                'sakit' => 'warning',
                                'cuti' => 'primary'
                            ];
                            $color = $statusColors[$absensi->status] ?? 'secondary';
                        @endphp
                        <span class="badge bg-{{ $color }} px-3 py-2">{{ ucfirst($absensi->status) }}</span>
                    </div>
                </div>

                @if($absensi->status == 'hadir' && $absensi->jam_masuk)
                    @php
                        $jamMasuk = \App\Models\Setting::getValue('jam_masuk', '08:00:00');
                        $toleransi = (int)\App\Models\Setting::getValue('toleransi_keterlambatan', '15');
                        $jamMasukDateTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
                        $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransi);
                        $jamMasukUser = \Carbon\Carbon::parse($absensi->jam_masuk);
                        $isLate = $jamMasukUser->gt($batasKeterlambatan);
                    @endphp
                    <div class="mb-3">
                        <strong>Ketepatan Waktu:</strong>
                        <div class="mt-1">
                            @if($isLate)
                                @php
                                    $minutesLate = $jamMasukUser->diffInMinutes($jamMasukDateTime);
                                @endphp
                                <span class="badge bg-warning px-3 py-2">Terlambat {{ $minutesLate }} menit</span>
                            @else
                                <span class="badge bg-success px-3 py-2">Tepat Waktu</span>
                            @endif
                        </div>
                    </div>
                @endif

                @if($absensi->keterangan)
                    <div class="mb-3">
                        <strong>Keterangan:</strong>
                        <p class="mb-0">{{ $absensi->keterangan }}</p>
                    </div>
                @endif

                <div class="mb-3">
                    <strong>Dibuat:</strong>
                    <p class="mb-0">{{ $absensi->created_at->format('d/m/Y H:i') }}</p>
                </div>

                @if($absensi->updated_at != $absensi->created_at)
                    <div class="mb-3">
                        <strong>Terakhir Diupdate:</strong>
                        <p class="mb-0">{{ $absensi->updated_at->format('d/m/Y H:i') }}</p>
                    </div>
                @endif
            </div>
        </div>

        @if(in_array($absensi->status, ['izin', 'sakit', 'cuti']))
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>Status Approval</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Status Approval:</strong>
                        <div class="mt-1">
                            @if($absensi->status_approval == 'approved')
                                <span class="badge bg-success px-3 py-2">Disetujui</span>
                            @elseif($absensi->status_approval == 'rejected')
                                <span class="badge bg-danger px-3 py-2">Ditolak</span>
                            @else
                                <span class="badge bg-warning px-3 py-2">Pending</span>
                            @endif
                        </div>
                    </div>

                    @if($absensi->approval_note)
                        <div class="mb-3">
                            <strong>Catatan Approval:</strong>
                            <p class="mb-0 text-muted">{{ $absensi->approval_note }}</p>
                        </div>
                    @endif

                    @if($absensi->approval_at)
                        <div class="mb-3">
                            <strong>Tanggal Approval:</strong>
                            <p class="mb-0">{{ \Carbon\Carbon::parse($absensi->approval_at)->format('d/m/Y H:i') }}</p>
                        </div>
                    @endif

                    <div class="mt-3">
                        <a href="{{ route('admin.approval.show', $absensi->id) }}"
                           class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye me-1"></i>Lihat Detail Pengajuan
                        </a>
                    </div>
                </div>
            </div>
        @endif

        @if(in_array($absensi->status, ['alpha', 'hadir']) && $absensi->salaryDeductions()->exists())
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="bi bi-cash-stack me-2"></i>Potongan Gaji Terkait</h6>
                </div>
                <div class="card-body">
                    @foreach($absensi->salaryDeductions as $deduction)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong>{{ ucfirst($deduction->jenis) }}:</strong>
                                <span class="text-muted">{{ $deduction->keterangan }}</span>
                            </div>
                            <div>
                                <span class="badge bg-danger">Rp {{ number_format($deduction->jumlah_potongan) }}</span>
                            </div>
                        </div>
                    @endforeach

                    <div class="alert alert-info border-0 mt-3 mb-0">
                        <small>
                            <i class="bi bi-info-circle me-1"></i>
                            <strong>Auto-Sync:</strong> Jika status absensi diubah, potongan gaji akan otomatis menyesuaikan.
                        </small>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

@if($absensi->foto_masuk || $absensi->foto_keluar || $absensi->tanda_tangan)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0"><i class="bi bi-camera me-2"></i>Dokumentasi</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @if($absensi->foto_masuk)
                            <div class="col-md-4 mb-3">
                                <h6 class="fw-bold">Foto Absen Masuk</h6>
                                <div class="border rounded p-2 text-center">
                                    <img src="{{ asset('storage/' . $absensi->foto_masuk) }}"
                                         class="img-fluid rounded" style="max-height: 200px;" alt="Foto Masuk">
                                </div>
                            </div>
                        @endif

                        @if($absensi->foto_keluar)
                            <div class="col-md-4 mb-3">
                                <h6 class="fw-bold">Foto Absen Keluar</h6>
                                <div class="border rounded p-2 text-center">
                                    <img src="{{ asset('storage/' . $absensi->foto_keluar) }}"
                                         class="img-fluid rounded" style="max-height: 200px;" alt="Foto Keluar">
                                </div>
                            </div>
                        @endif

                        @if($absensi->tanda_tangan)
                            <div class="col-md-4 mb-3">
                                <h6 class="fw-bold">Tanda Tangan Digital</h6>
                                <div class="border rounded p-2 text-center">
                                    <img src="{{ asset($absensi->tanda_tangan) }}"
                                         alt="Tanda Tangan" class="img-fluid" style="max-height: 150px;">
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

@if($absensi->foto_masuk || $absensi->foto_keluar || $absensi->tanda_tangan)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0"><i class="bi bi-camera me-2"></i>Dokumentasi</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @if($absensi->foto_masuk)
                            <div class="col-md-4 mb-3">
                                <h6 class="fw-bold">Foto Absen Masuk</h6>
                                <div class="border rounded p-2 text-center">
                                    <img src="{{ asset('storage/' . $absensi->foto_masuk) }}"
                                         class="img-fluid rounded" style="max-height: 200px;" alt="Foto Masuk">
                                </div>
                            </div>
                        @endif

                        @if($absensi->foto_keluar)
                            <div class="col-md-4 mb-3">
                                <h6 class="fw-bold">Foto Absen Keluar</h6>
                                <div class="border rounded p-2 text-center">
                                    <img src="{{ asset('storage/' . $absensi->foto_keluar) }}"
                                         class="img-fluid rounded" style="max-height: 200px;" alt="Foto Keluar">
                                </div>
                            </div>
                        @endif

                        @if($absensi->tanda_tangan)
                            <div class="col-md-4 mb-3">
                                <h6 class="fw-bold">Tanda Tangan Digital</h6>
                                <div class="border rounded p-2 text-center">
                                    <img src="{{ asset($absensi->tanda_tangan) }}"
                                         alt="Tanda Tangan" class="img-fluid" style="max-height: 150px;">
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
