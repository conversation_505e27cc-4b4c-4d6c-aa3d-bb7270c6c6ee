<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Surat Izin/Cuti/Sakit</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        .header h2 {
            margin: 0;
            padding: 0;
            font-size: 18px;
        }
        .header p {
            margin: 5px 0;
            font-size: 14px;
        }
        .content {
            margin-bottom: 30px;
        }
        .content h3 {
            text-align: center;
            text-decoration: underline;
            margin-bottom: 20px;
        }
        .content p {
            text-align: justify;
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            margin-bottom: 20px;
        }
        table td {
            padding: 5px;
            vertical-align: top;
        }
        .signature {
            margin-top: 50px;
            text-align: right;
        }
        .signature-content {
            display: inline-block;
            text-align: center;
        }
        .signature-line {
            margin-top: 50px;
            border-bottom: 1px solid #000;
            width: 200px;
            display: inline-block;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>{{ $namaPerusahaan ?? 'PT Absensi Pegawai' }}</h2>
        <p>{{ $alamatPerusahaan ?? 'Jalan Kolonel Masturi No 138' }}</p>
    </div>

    <div class="content">
        <h3>SURAT {{ strtoupper($izin->jenis ?? $izin->status ?? 'IZIN') }}</h3>

        <p>Yang bertanda tangan di bawah ini:</p>

        <table>
            <tr>
                <td width="30%">Nama</td>
                <td width="5%">:</td>
                <td width="65%">{{ isset($izin->user) ? $izin->user->name : 'Nama Pegawai' }}</td>
            </tr>
            <tr>
                <td>Jabatan</td>
                <td>:</td>
                <td>{{ isset($izin->user) && isset($izin->user->jabatan) ? $izin->user->jabatan : 'Pegawai' }}</td>
            </tr>
            <tr>
                <td>NIK</td>
                <td>:</td>
                <td>{{ isset($izin->user) && isset($izin->user->nik) ? $izin->user->nik : '-' }}</td>
            </tr>
        </table>

        <p>Dengan ini mengajukan {{ $izin->jenis ?? $izin->status ?? 'izin' }} pada tanggal:</p>

        @php
            try {
                $tanggalMulai = isset($izin->tanggal_mulai) ? \Carbon\Carbon::parse($izin->tanggal_mulai)->format('d F Y') : date('d F Y');
                $tanggalSelesai = isset($izin->tanggal_selesai) ? \Carbon\Carbon::parse($izin->tanggal_selesai)->format('d F Y') : $tanggalMulai;

                if ($tanggalMulai == $tanggalSelesai) {
                    $tanggalText = $tanggalMulai;
                } else {
                    $tanggalText = "{$tanggalMulai} s/d {$tanggalSelesai}";
                }
            } catch (\Exception $e) {
                $tanggalText = date('d F Y');
            }
        @endphp

        <p class="text-center"><strong>{{ $tanggalText }}</strong></p>

        <p>Dengan alasan sebagai berikut:</p>
        <p class="text-center"><strong>{{ $izin->keterangan ?? '-' }}</strong></p>

        <p>Demikian surat {{ $izin->jenis ?? $izin->status ?? 'izin' }} ini saya buat dengan sebenarnya. Atas perhatian dan izin yang diberikan, saya ucapkan terima kasih.</p>
    </div>

    <div class="signature">
        <div class="signature-content">
            <p>{{ isset($alamatPerusahaan) && !empty($alamatPerusahaan) ? explode(',', $alamatPerusahaan)[0] : 'Jakarta' }}, {{ date('d F Y') }}</p>
            <p>Hormat saya,</p>

            @if(isset($izin->tanda_tangan) && $izin->tanda_tangan)
                <img src="{{ public_path($izin->tanda_tangan) }}" alt="Tanda Tangan" style="height: 80px;">
            @else
                <div class="signature-line"></div>
            @endif

            <p><strong>{{ isset($izin->user) ? $izin->user->name : 'Nama Pegawai' }}</strong></p>
        </div>
    </div>

    <div style="margin-top: 50px; border-top: 1px dashed #000; padding-top: 10px;">
        <h4 class="text-center">PERSETUJUAN</h4>

        <table>
            <tr>
                <td width="30%">Status</td>
                <td width="5%">:</td>
                <td width="65%">
                    @if(isset($izin->status) && $izin->status == 'approved')
                        Disetujui
                    @elseif(isset($izin->status) && $izin->status == 'rejected')
                        Ditolak
                    @else
                        Menunggu Persetujuan
                    @endif
                </td>
            </tr>
            @if(isset($izin->status) && $izin->status != 'pending')
                <tr>
                    <td>Tanggal Persetujuan</td>
                    <td>:</td>
                    <td>
                        @if(isset($izin->approval_at))
                            @php
                                try {
                                    echo \Carbon\Carbon::parse($izin->approval_at)->format('d F Y H:i:s');
                                } catch (\Exception $e) {
                                    echo '-';
                                }
                            @endphp
                        @else
                            -
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>Disetujui Oleh</td>
                    <td>:</td>
                    <td>
                        @if(isset($izin->approver) && isset($izin->approver->name))
                            {{ $izin->approver->name }}
                        @else
                            Admin
                        @endif
                    </td>
                </tr>
                <tr>
                    <td>Catatan</td>
                    <td>:</td>
                    <td>{{ $izin->catatan_admin ?? $izin->approval_note ?? '-' }}</td>
                </tr>
            @endif
        </table>

        @if(isset($izin->status) && $izin->status != 'pending')
            <div class="signature" style="margin-top: 20px;">
                <div class="signature-content">
                    <p>Menyetujui,</p>
                    <div class="signature-line"></div>
                    <p><strong>
                        @if(isset($izin->approver) && isset($izin->approver->name))
                            {{ $izin->approver->name }}
                        @else
                            Admin
                        @endif
                    </strong></p>
                </div>
            </div>
        @endif
    </div>
</body>
</html>
