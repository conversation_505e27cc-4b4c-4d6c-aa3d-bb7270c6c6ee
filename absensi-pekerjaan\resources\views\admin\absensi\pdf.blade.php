<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Data Absensi - {{ \App\Facades\Tanggal::formatTanggalLengkap($tanggalFilter) }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }

        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }

        .header h2 {
            margin: 5px 0;
            font-size: 16px;
            color: #666;
        }

        .header p {
            margin: 5px 0;
            color: #888;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .stat-label {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }

        .stat-hadir { color: #28a745; }
        .stat-alpha { color: #dc3545; }
        .stat-izin { color: #ffc107; }
        .stat-total { color: #17a2b8; }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: center;
        }

        .text-center {
            text-align: center;
        }

        .badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }

        .badge-success { background-color: #28a745; }
        .badge-danger { background-color: #dc3545; }
        .badge-warning { background-color: #ffc107; color: #333; }
        .badge-info { background-color: #17a2b8; }
        .badge-primary { background-color: #007bff; }

        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 11px;
            color: #666;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ $namaPerusahaan }}</h1>
        <h2>LAPORAN DATA ABSENSI PEGAWAI</h2>
        <p>Tanggal: {{ \App\Facades\Tanggal::formatTanggalLengkap($tanggalFilter) }}</p>
        <p>Dicetak pada: {{ \App\Facades\Tanggal::formatTanggalLengkap(date('Y-m-d')) }} pukul {{ date('H:i:s') }}</p>
    </div>

    <div class="stats">
        <div class="stat-item">
            <div class="stat-number stat-hadir">{{ $totalHadir }}</div>
            <div class="stat-label">Pegawai Hadir</div>
        </div>
        <div class="stat-item">
            <div class="stat-number stat-alpha">{{ $totalAlpha }}</div>
            <div class="stat-label">Alpha</div>
        </div>
        <div class="stat-item">
            <div class="stat-number stat-izin">{{ $totalIzin }}</div>
            <div class="stat-label">Izin/Sakit/Cuti</div>
        </div>
        <div class="stat-item">
            <div class="stat-number stat-total">{{ $totalPegawai }}</div>
            <div class="stat-label">Total Pegawai</div>
        </div>
    </div>

    @if($allAbsen->count() > 0)
        <table>
            <thead>
                <tr>
                    <th width="5%">No</th>
                    <th width="25%">Nama Pegawai</th>
                    <th width="15%">Tanggal</th>
                    <th width="12%">Jam Masuk</th>
                    <th width="12%">Jam Keluar</th>
                    <th width="10%">Status</th>
                    <th width="21%">Keterangan</th>
                </tr>
            </thead>
            <tbody>
                @foreach($allAbsen as $index => $a)
                    <tr>
                        <td class="text-center">{{ $index + 1 }}</td>
                        <td>
                            <strong>{{ $a->user->name }}</strong>
                            @if($a->user->nik)
                                <br><small>NIK: {{ $a->user->nik }}</small>
                            @endif
                        </td>
                        <td class="text-center">
                            {{ \App\Facades\Tanggal::formatTanggal($a->tanggal) }}<br>
                            <small>{{ \App\Facades\Tanggal::formatHari($a->tanggal) }}</small>
                        </td>
                        <td class="text-center">
                            @if(in_array($a->status, ['izin', 'sakit', 'cuti']))
                                -
                            @elseif($a->jam_masuk)
                                {{ \Carbon\Carbon::parse($a->jam_masuk)->format('H:i') }}
                                @php
                                    try {
                                        $jamMasuk = \App\Models\Setting::getValue('jam_masuk', '08:00:00');
                                        $toleransi = (int)\App\Models\Setting::getValue('toleransi_keterlambatan', '15');

                                        // Pastikan format jam masuk konsisten
                                        if (strlen($jamMasuk) == 5) {
                                            $jamMasuk .= ':00';
                                        }

                                        $jamMasukDateTime = \Carbon\Carbon::createFromFormat('H:i:s', $jamMasuk);
                                        $batasKeterlambatan = $jamMasukDateTime->copy()->addMinutes($toleransi);
                                        $jamMasukUser = \Carbon\Carbon::parse($a->jam_masuk);
                                        $isTerlambat = $jamMasukUser->gt($batasKeterlambatan);
                                    } catch (\Exception $e) {
                                        $isTerlambat = false;
                                    }
                                @endphp
                                @if($isTerlambat)
                                    @php
                                        try {
                                            $selisihMenit = $jamMasukUser->diffInMinutes($jamMasukDateTime);
                                        } catch (\Exception $e) {
                                            $selisihMenit = 0;
                                        }
                                    @endphp
                                    <br><small style="color: #dc3545;">Terlambat {{ $selisihMenit }} menit</small>
                                @else
                                    <br><small style="color: #28a745;">Tepat waktu</small>
                                @endif
                            @else
                                -
                            @endif
                        </td>
                        <td class="text-center">
                            @if(in_array($a->status, ['izin', 'sakit', 'cuti']))
                                -
                            @elseif($a->jam_keluar)
                                {{ \Carbon\Carbon::parse($a->jam_keluar)->format('H:i') }}
                                @if($a->jam_masuk)
                                    @php
                                        $jamMasuk = \Carbon\Carbon::parse($a->jam_masuk);
                                        $jamKeluar = \Carbon\Carbon::parse($a->jam_keluar);
                                        $durasi = $jamKeluar->diff($jamMasuk);
                                    @endphp
                                    <br><small style="color: #17a2b8;">{{ $durasi->h }}j {{ $durasi->i }}m</small>
                                @endif
                            @else
                                -
                            @endif
                        </td>
                        <td class="text-center">
                            @php
                                $statusColors = [
                                    'hadir' => 'success',
                                    'alpha' => 'danger',
                                    'izin' => 'warning',
                                    'sakit' => 'info',
                                    'cuti' => 'primary'
                                ];
                                $color = $statusColors[$a->status] ?? 'secondary';
                            @endphp
                            <span class="badge badge-{{ $color }}">
                                {{ ucfirst($a->status) }}
                            </span>
                        </td>
                        <td>
                            {{ $a->keterangan ?? '-' }}
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @else
        <div class="no-data">
            <p>Tidak ada data absensi untuk tanggal yang dipilih.</p>
        </div>
    @endif

    <div class="footer">
        <p>Total {{ $allAbsen->count() }} data absensi</p>
        <p>Laporan ini digenerate secara otomatis oleh sistem</p>
    </div>
</body>
</html>
