<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('nik')->nullable()->after('name');
            $table->string('jabatan')->nullable()->after('nik');
            $table->string('departemen')->nullable()->after('jabatan');
            $table->string('no_hp')->nullable()->after('departemen');
            $table->string('alamat')->nullable()->after('no_hp');
            $table->date('tanggal_lahir')->nullable()->after('alamat');
            $table->date('tanggal_bergabung')->nullable()->after('tanggal_lahir');
            $table->string('foto_profil')->nullable()->after('tanggal_bergabung');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'nik',
                'jabatan',
                'departemen',
                'no_hp',
                'alamat',
                'tanggal_lahir',
                'tanggal_bergabung',
                'foto_profil'
            ]);
        });
    }
};
