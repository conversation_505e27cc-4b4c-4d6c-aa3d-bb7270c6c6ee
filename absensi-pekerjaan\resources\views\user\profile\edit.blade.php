@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-pencil-square me-2"></i>Edit Profil</h5>
                    <div>
                        <a href="{{ route('profile.show') }}" class="btn btn-sm btn-light">
                            <i class="bi bi-arrow-left me-1"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body p-4">
                    <form action="{{ route('profile.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-lg-4 mb-4">
                                <div class="card border-0 shadow-sm text-center h-100">
                                    <div class="card-body p-4">
                                        <div class="mb-4 position-relative">
                                            @if($user->foto_profil)
                                                <img src="{{ asset($user->foto_profil) }}" alt="{{ $user->name }}" id="preview-foto" class="img-thumbnail rounded-circle shadow" style="width: 180px; height: 180px; object-fit: cover;">
                                            @else
                                                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto shadow" id="preview-placeholder" style="width: 180px; height: 180px;">
                                                    <i class="bi bi-person" style="font-size: 5rem; color: #6c757d;"></i>
                                                </div>
                                                <img src="" alt="" id="preview-foto" class="img-thumbnail rounded-circle shadow" style="width: 180px; height: 180px; object-fit: cover; display: none;">
                                            @endif
                                        </div>

                                        <div class="mb-3">
                                            <label for="foto_profil" class="form-label fw-bold">Foto Profil</label>
                                            <div class="input-group">
                                                <input type="file" class="form-control @error('foto_profil') is-invalid @enderror" id="foto_profil" name="foto_profil" accept="image/*">
                                                <button class="btn btn-outline-secondary" type="button" id="reset-foto">
                                                    <i class="bi bi-x-circle"></i>
                                                </button>
                                            </div>
                                            @error('foto_profil')
                                                <div class="text-danger small mt-1">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Format: JPG, PNG, JPEG. Maks: 2MB</div>
                                        </div>

                                        <div class="alert alert-info small">
                                            <i class="bi bi-info-circle me-1"></i> Unggah foto terbaru Anda untuk memperbarui profil
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-8">
                                <div class="card border-0 shadow-sm mb-4">
                                    <div class="card-header bg-light py-3">
                                        <h6 class="mb-0"><i class="bi bi-person-vcard me-2"></i>Informasi Pribadi</h6>
                                    </div>
                                    <div class="card-body p-4">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="name" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-person"></i></span>
                                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                                </div>
                                                @error('name')
                                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                                    <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                                </div>
                                                @error('email')
                                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <label for="nik" class="form-label">NIK</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-credit-card"></i></span>
                                                    <input type="text" class="form-control @error('nik') is-invalid @enderror" id="nik" name="nik" value="{{ old('nik', $user->nik) }}">
                                                </div>
                                                @error('nik')
                                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <label for="tanggal_lahir" class="form-label">Tanggal Lahir</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-calendar-date"></i></span>
                                                    @php
                                                        $tanggalLahirValue = '';
                                                        if ($user->tanggal_lahir) {
                                                            try {
                                                                $tanggalLahirValue = \Carbon\Carbon::parse($user->tanggal_lahir)->format('Y-m-d');
                                                            } catch (\Exception $e) {
                                                                $tanggalLahirValue = $user->tanggal_lahir;
                                                            }
                                                        }
                                                    @endphp
                                                    <input type="date" class="form-control @error('tanggal_lahir') is-invalid @enderror" id="tanggal_lahir" name="tanggal_lahir" value="{{ old('tanggal_lahir', $tanggalLahirValue) }}">
                                                </div>
                                                @error('tanggal_lahir')
                                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <label for="no_hp" class="form-label">No. HP</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-telephone"></i></span>
                                                    <input type="text" class="form-control @error('no_hp') is-invalid @enderror" id="no_hp" name="no_hp" value="{{ old('no_hp', $user->no_hp) }}">
                                                </div>
                                                @error('no_hp')
                                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <label for="jenis_kelamin" class="form-label">Jenis Kelamin</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-gender-ambiguous"></i></span>
                                                    <select class="form-select @error('jenis_kelamin') is-invalid @enderror" id="jenis_kelamin" name="jenis_kelamin">
                                                        <option value="">Pilih Jenis Kelamin</option>
                                                        <option value="L" {{ old('jenis_kelamin', $user->jenis_kelamin) == 'L' ? 'selected' : '' }}>Laki-laki</option>
                                                        <option value="P" {{ old('jenis_kelamin', $user->jenis_kelamin) == 'P' ? 'selected' : '' }}>Perempuan</option>
                                                    </select>
                                                </div>
                                                @error('jenis_kelamin')
                                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="col-12 mb-3">
                                                <label for="alamat" class="form-label">Alamat</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-geo-alt"></i></span>
                                                    <textarea class="form-control @error('alamat') is-invalid @enderror" id="alamat" name="alamat" rows="3">{{ old('alamat', $user->alamat) }}</textarea>
                                                </div>
                                                @error('alamat')
                                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card border-0 shadow-sm">
                                    <div class="card-header bg-light py-3">
                                        <h6 class="mb-0"><i class="bi bi-briefcase me-2"></i>Informasi Pekerjaan</h6>
                                    </div>
                                    <div class="card-body p-4">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="jabatan" class="form-label">Jabatan</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-person-badge"></i></span>
                                                    <input type="text" class="form-control @error('jabatan') is-invalid @enderror" id="jabatan" name="jabatan" value="{{ old('jabatan', $user->jabatan) }}">
                                                </div>
                                                @error('jabatan')
                                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <label for="departemen" class="form-label">Departemen</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-building"></i></span>
                                                    <input type="text" class="form-control @error('departemen') is-invalid @enderror" id="departemen" name="departemen" value="{{ old('departemen', $user->departemen) }}">
                                                </div>
                                                @error('departemen')
                                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <label for="tanggal_bergabung" class="form-label">Tanggal Bergabung</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-calendar-check"></i></span>
                                                    @php
                                                        $tanggalBergabungValue = '';
                                                        if ($user->tanggal_bergabung) {
                                                            try {
                                                                $tanggalBergabungValue = \Carbon\Carbon::parse($user->tanggal_bergabung)->format('Y-m-d');
                                                            } catch (\Exception $e) {
                                                                $tanggalBergabungValue = $user->tanggal_bergabung;
                                                            }
                                                        }
                                                    @endphp
                                                    <input type="date" class="form-control @error('tanggal_bergabung') is-invalid @enderror" id="tanggal_bergabung" name="tanggal_bergabung" value="{{ old('tanggal_bergabung', $tanggalBergabungValue) }}">
                                                </div>
                                                @error('tanggal_bergabung')
                                                    <div class="text-danger small mt-1">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-1"></i> Simpan Perubahan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Preview foto profil
    const fotoInput = document.getElementById('foto_profil');
    const previewFoto = document.getElementById('preview-foto');
    const previewPlaceholder = document.getElementById('preview-placeholder');
    const resetFotoBtn = document.getElementById('reset-foto');

    // Fungsi untuk menampilkan preview foto
    fotoInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                previewFoto.src = e.target.result;
                previewFoto.style.display = 'block';
                if (previewPlaceholder) {
                    previewPlaceholder.style.display = 'none';
                }
            }

            reader.readAsDataURL(this.files[0]);
        }
    });

    // Fungsi untuk reset foto
    resetFotoBtn.addEventListener('click', function() {
        fotoInput.value = '';
        if (previewPlaceholder) {
            previewPlaceholder.style.display = 'flex';
            previewFoto.style.display = 'none';
            previewFoto.src = '';
        }
    });
});
</script>
@endsection
