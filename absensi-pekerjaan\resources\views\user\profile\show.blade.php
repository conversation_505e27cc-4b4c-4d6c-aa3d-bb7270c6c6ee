@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-person-circle me-2"></i>Profil <PERSON>a</h5>
                    <div>
                        @if(auth()->user()->role == 'admin')
                            <a href="{{ route('admin.dashboard') }}" class="btn btn-sm btn-light">
                                <i class="bi bi-arrow-left me-1"></i> Kembali ke Dashboard Admin
                            </a>
                        @else
                            <a href="{{ route('dashboard') }}" class="btn btn-sm btn-light">
                                <i class="bi bi-arrow-left me-1"></i> Kembali
                            </a>
                        @endif
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-lg-4 mb-4">
                            <div class="card border-0 shadow-sm text-center h-100">
                                <div class="card-body p-4">
                                    <div class="mb-4 position-relative">
                                        @if($user->foto_profil)
                                            <img src="{{ asset($user->foto_profil) }}" alt="{{ $user->name }}" class="img-thumbnail rounded-circle shadow" style="width: 180px; height: 180px; object-fit: cover;">
                                        @else
                                            <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto shadow" style="width: 180px; height: 180px;">
                                                <i class="bi bi-person" style="font-size: 5rem; color: #6c757d;"></i>
                                            </div>
                                        @endif

                                        @if($user->role == 'admin')
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                                Admin <i class="bi bi-shield-check ms-1"></i>
                                            </span>
                                        @else
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-success">
                                                Pegawai <i class="bi bi-person-badge ms-1"></i>
                                            </span>
                                        @endif
                                    </div>

                                    <h4 class="fw-bold mb-1">{{ $user->name }}</h4>
                                    <p class="text-muted mb-3">{{ $user->jabatan ?? 'Belum diatur' }}</p>

                                    <div class="d-flex justify-content-center mb-3">
                                        @if($user->email)
                                            <div class="d-flex align-items-center me-3">
                                                <i class="bi bi-envelope text-primary me-2"></i>
                                                <span>{{ $user->email }}</span>
                                            </div>
                                        @endif

                                        @if($user->no_hp)
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-telephone text-success me-2"></i>
                                                <span>{{ $user->no_hp }}</span>
                                            </div>
                                        @endif
                                    </div>

                                    <div class="d-grid gap-2">
                                        <a href="{{ route('profile.edit') }}" class="btn btn-primary">
                                            <i class="bi bi-pencil-square me-1"></i> Edit Profil
                                        </a>
                                        <a href="{{ route('profile.password.edit') }}" class="btn btn-outline-secondary">
                                            <i class="bi bi-key me-1"></i> Ganti Password
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-8">
                            <div class="card border-0 shadow-sm mb-4">
                                <div class="card-header bg-light py-3">
                                    <h6 class="mb-0"><i class="bi bi-person-vcard me-2"></i>Informasi Pribadi</h6>
                                </div>
                                <div class="card-body p-4">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <div class="info-item">
                                                <label class="text-muted small mb-1">Nama Lengkap</label>
                                                <div class="fs-5">{{ $user->name }}</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="info-item">
                                                <label class="text-muted small mb-1">Email</label>
                                                <div class="fs-5">{{ $user->email }}</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="info-item">
                                                <label class="text-muted small mb-1">NIK</label>
                                                <div class="fs-5">
                                                    @if($user->nik)
                                                        <span class="badge bg-light text-dark border">{{ $user->nik }}</span>
                                                    @else
                                                        <span class="text-muted fst-italic">Belum diatur</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="info-item">
                                                <label class="text-muted small mb-1">Tanggal Lahir</label>
                                                <div class="fs-5">
                                                    @if($user->tanggal_lahir)
                                                        <i class="bi bi-calendar-date me-1 text-primary"></i>
                                                        {{ \App\Facades\Tanggal::formatTanggal($user->tanggal_lahir) }}
                                                    @else
                                                        <span class="text-muted fst-italic">Belum diatur</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="info-item">
                                                <label class="text-muted small mb-1">No. HP</label>
                                                <div class="fs-5">
                                                    @if($user->no_hp)
                                                        <i class="bi bi-telephone me-1 text-success"></i>
                                                        {{ $user->no_hp }}
                                                    @else
                                                        <span class="text-muted fst-italic">Belum diatur</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="info-item">
                                                <label class="text-muted small mb-1">Jenis Kelamin</label>
                                                <div class="fs-5">
                                                    @if($user->jenis_kelamin)
                                                        @if($user->jenis_kelamin == 'L')
                                                            <i class="bi bi-gender-male me-1 text-primary"></i> Laki-laki
                                                        @else
                                                            <i class="bi bi-gender-female me-1 text-danger"></i> Perempuan
                                                        @endif
                                                    @else
                                                        <span class="text-muted fst-italic">Belum diatur</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="info-item">
                                                <label class="text-muted small mb-1">Alamat</label>
                                                <div class="fs-5">
                                                    @if($user->alamat)
                                                        <i class="bi bi-geo-alt me-1 text-danger"></i>
                                                        {{ $user->alamat }}
                                                    @else
                                                        <span class="text-muted fst-italic">Belum diatur</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-light py-3">
                                    <h6 class="mb-0"><i class="bi bi-briefcase me-2"></i>Informasi Pekerjaan</h6>
                                </div>
                                <div class="card-body p-4">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <div class="info-item">
                                                <label class="text-muted small mb-1">Jabatan</label>
                                                <div class="fs-5">
                                                    @if($user->jabatan)
                                                        <span class="badge bg-primary">{{ $user->jabatan }}</span>
                                                    @else
                                                        <span class="text-muted fst-italic">Belum diatur</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="info-item">
                                                <label class="text-muted small mb-1">Departemen</label>
                                                <div class="fs-5">
                                                    @if($user->departemen)
                                                        <span class="badge bg-info">{{ $user->departemen }}</span>
                                                    @else
                                                        <span class="text-muted fst-italic">Belum diatur</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="info-item">
                                                <label class="text-muted small mb-1">Tanggal Bergabung</label>
                                                <div class="fs-5">
                                                    @if($user->tanggal_bergabung)
                                                        <i class="bi bi-calendar-check me-1 text-success"></i>
                                                        {{ \App\Facades\Tanggal::formatTanggal($user->tanggal_bergabung) }}
                                                    @else
                                                        <span class="text-muted fst-italic">Belum diatur</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="info-item">
                                                <label class="text-muted small mb-1">Status</label>
                                                <div class="fs-5">
                                                    @if($user->status)
                                                        <span class="badge bg-success">{{ $user->status }}</span>
                                                    @else
                                                        <span class="badge bg-success">Aktif</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-item {
    padding: 10px;
    border-radius: 8px;
    background-color: #f8f9fa;
    height: 100%;
}
</style>
@endsection
