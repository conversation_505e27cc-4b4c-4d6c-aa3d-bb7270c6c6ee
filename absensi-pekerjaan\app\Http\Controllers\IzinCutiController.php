<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\IzinCuti;
use Illuminate\Support\Facades\Storage;

class IzinCutiController extends Controller
{
    public function index()
    {
        $izinCutis = IzinCuti::where('user_id', auth()->id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('user.izin_cuti.index', compact('izinCutis'));
    }

    public function create()
    {
        return view('user.izin_cuti.create');
    }

    public function store(Request $request)
    {
        try {
            // Validasi input
            $request->validate([
                'jenis' => 'required|in:izin,sakit,cuti',
                'tanggal_mulai' => 'required|date',
                'tanggal_selesai' => 'required|date|after_or_equal:tanggal_mulai',
                'keterangan' => 'required|string',
                'tanda_tangan' => 'required',
                'dokumen' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            ]);

            // Simpan tanda tangan
            $tandaTanganPath = null;
            if ($request->filled('tanda_tangan')) {
                $tandaTanganData = $request->tanda_tangan;
                if (strpos($tandaTanganData, 'data:image') === 0) {
                    $tandaTanganData = substr($tandaTanganData, strpos($tandaTanganData, ',') + 1);
                    $tandaTanganData = base64_decode($tandaTanganData);
                    $tandaTanganPath = 'tanda_tangan/' . time() . '_' . auth()->id() . '.png';

                    // Pastikan direktori ada
                    if (!file_exists(public_path('tanda_tangan'))) {
                        mkdir(public_path('tanda_tangan'), 0755, true);
                    }

                    file_put_contents(public_path($tandaTanganPath), $tandaTanganData);
                }
            }

            // Simpan dokumen jika ada
            $dokumenPath = null;
            if ($request->hasFile('dokumen')) {
                $dokumen = $request->file('dokumen');
                $dokumenPath = $dokumen->store('dokumen', 'public');
            }

            // Buat record izin/cuti
            IzinCuti::create([
                'user_id' => auth()->id(),
                'jenis' => $request->jenis,
                'tanggal_mulai' => $request->tanggal_mulai,
                'tanggal_selesai' => $request->tanggal_selesai,
                'keterangan' => $request->keterangan,
                'tanda_tangan' => $tandaTanganPath,
                'dokumen' => $dokumenPath,
                'status' => 'pending',
            ]);

            return redirect()->route('izin-cuti.index')->with('success', 'Pengajuan ' . $request->jenis . ' berhasil disimpan dan menunggu persetujuan');
        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage())->withInput();
        }
    }

    public function show($id)
    {
        // Langsung arahkan ke halaman error
        return view('user.izin_cuti.show');
    }

    public function destroy($id)
    {
        $izinCuti = IzinCuti::findOrFail($id);

        // Pastikan user hanya bisa menghapus data miliknya dan status masih pending
        if ($izinCuti->user_id != auth()->id() || $izinCuti->status != 'pending') {
            return redirect()->route('izin-cuti.index')->with('error', 'Anda tidak dapat menghapus pengajuan ini');
        }

        // Hapus file terkait
        if ($izinCuti->tanda_tangan && file_exists(public_path($izinCuti->tanda_tangan))) {
            unlink(public_path($izinCuti->tanda_tangan));
        }

        if ($izinCuti->dokumen) {
            Storage::disk('public')->delete($izinCuti->dokumen);
        }

        $izinCuti->delete();

        return redirect()->route('izin-cuti.index')->with('success', 'Pengajuan berhasil dihapus');
    }
}
