<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            Log::info('AdminMiddleware: Checking if user is admin');

            // Pastikan pengguna memiliki role 'admin'
            if (Auth::check() && Auth::user() && Auth::user()->role === 'admin') {
                Log::info('AdminMiddleware: User is admin', [
                    'user_id' => Auth::id(),
                    'user_name' => Auth::user()->name,
                    'user_role' => Auth::user()->role
                ]);
                return $next($request);
            }

            // Redirect jika bukan admin
            Log::warning('AdminMiddleware: User is not admin', [
                'user_id' => Auth::id(),
                'user_role' => Auth::user() ? Auth::user()->role : 'not authenticated'
            ]);

            return redirect('/dashboard')->with('error', 'Akses ditolak. Anda tidak memiliki izin admin.');

        } catch (\Exception $e) {
            Log::error('AdminMiddleware error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect('/login')->with('error', 'Terjadi kesalahan sistem. Silakan login kembali.');
        }
    }

}
