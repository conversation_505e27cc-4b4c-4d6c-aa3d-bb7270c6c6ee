@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">Form Pengajuan Izin/Cuti</h5>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <form method="POST" action="{{ route('izin.submit') }}" id="izinForm" enctype="multipart/form-data">
                        @csrf

                        <div class="mb-3">
                            <label for="status" class="form-label">Jenis <PERSON>gajuan</label>
                            <select name="status" id="status" class="form-select @error('status') is-invalid @enderror" required>
                                <option value=""><PERSON>lih <PERSON></option>
                                <option value="izin" {{ old('status') == 'izin' ? 'selected' : '' }}>Izin</option>
                                <option value="sakit" {{ old('status') == 'sakit' ? 'selected' : '' }}>Sakit</option>
                                <option value="cuti" {{ old('status') == 'cuti' ? 'selected' : '' }}>Cuti</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="tanggal_mulai" class="form-label">Tanggal Mulai</label>
                                <input type="date" name="tanggal_mulai" id="tanggal_mulai" class="form-control @error('tanggal_mulai') is-invalid @enderror" value="{{ old('tanggal_mulai') }}" required>
                                @error('tanggal_mulai')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="tanggal_selesai" class="form-label">Tanggal Selesai</label>
                                <input type="date" name="tanggal_selesai" id="tanggal_selesai" class="form-control @error('tanggal_selesai') is-invalid @enderror" value="{{ old('tanggal_selesai') }}" required>
                                @error('tanggal_selesai')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="keterangan" class="form-label">Keterangan/Alasan</label>
                            <textarea name="keterangan" id="keterangan" class="form-control @error('keterangan') is-invalid @enderror" rows="4" required>{{ old('keterangan') }}</textarea>
                            @error('keterangan')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="dokumen" class="form-label">Dokumen Pendukung (opsional)</label>
                            <input type="file" name="dokumen" id="dokumen" class="form-control @error('dokumen') is-invalid @enderror">
                            <small class="text-muted">Format: PDF, JPG, JPEG, PNG (Maks. 2MB)</small>
                            @error('dokumen')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="signature-pad" class="form-label">Tanda Tangan Digital</label>
                            <div class="border rounded p-2">
                                <canvas id="signature-pad" class="signature-pad" width="100%" height="200"></canvas>
                            </div>
                            <div class="d-flex justify-content-end mt-2">
                                <button type="button" id="clear-signature" class="btn btn-sm btn-secondary">
                                    <i class="bi bi-eraser"></i> Hapus
                                </button>
                            </div>
                            <input type="hidden" name="tanda_tangan" id="tanda_tangan">
                            @error('tanda_tangan')
                                <div class="text-danger">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="/dashboard" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Kembali
                            </a>
                            <button type="submit" class="btn btn-warning" id="submitBtn">
                                <i class="bi bi-check-circle"></i> Kirim Pengajuan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tanda tangan digital
    const signaturePad = new SignaturePad(document.getElementById('signature-pad'), {
        backgroundColor: 'rgba(255, 255, 255, 0)',
        penColor: 'black'
    });

    document.getElementById('clear-signature').addEventListener('click', function() {
        signaturePad.clear();
    });

    // Form submit
    document.getElementById('izinForm').addEventListener('submit', function(e) {
        if (signaturePad.isEmpty()) {
            e.preventDefault();
            alert('Silakan isi tanda tangan Anda');
            return false;
        }

        // Simpan tanda tangan sebagai base64
        document.getElementById('tanda_tangan').value = signaturePad.toDataURL();
    });

    // Validasi tanggal
    document.getElementById('tanggal_selesai').addEventListener('change', function() {
        const tanggalMulai = new Date(document.getElementById('tanggal_mulai').value);
        const tanggalSelesai = new Date(this.value);

        if (tanggalSelesai < tanggalMulai) {
            alert('Tanggal selesai tidak boleh lebih awal dari tanggal mulai');
            this.value = document.getElementById('tanggal_mulai').value;
        }
    });
});
</script>

<style>
.signature-pad {
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;
    height: 200px;
    background-color: white;
}
</style>
@endsection
