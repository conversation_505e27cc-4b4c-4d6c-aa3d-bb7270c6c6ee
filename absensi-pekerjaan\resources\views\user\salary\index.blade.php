@extends('layouts.app')

@section('title', 'Slip Gaji')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-receipt me-2"></i>
                            Riwayat Slip Gaji
                        </h5>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filter -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <form method="GET" action="{{ route('salary.index') }}" class="d-flex gap-2">
                                <select name="bulan" class="form-select">
                                    <option value="">Semua Bulan</option>
                                    @for($i = 1; $i <= 12; $i++)
                                        <option value="{{ $i }}" {{ $bulan == $i ? 'selected' : '' }}>
                                            {{ DateTime::createFromFormat('!m', $i)->format('F') }}
                                        </option>
                                    @endfor
                                </select>
                                <select name="tahun" class="form-select">
                                    <option value="">Semua Tahun</option>
                                    @for($i = date('Y') - 2; $i <= date('Y'); $i++)
                                        <option value="{{ $i }}" {{ $tahun == $i ? 'selected' : '' }}>{{ $i }}</option>
                                    @endfor
                                </select>
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="bi bi-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Tabel Slip Gaji -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>No</th>
                                    <th>Periode</th>
                                    <th>Gaji Pokok</th>
                                    <th>Total Lembur</th>
                                    <th>Total Potongan</th>
                                    <th>Gaji Bersih</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($salaries as $index => $salary)
                                    <tr>
                                        <td>{{ $salaries->firstItem() + $index }}</td>
                                        <td>
                                            <div class="fw-bold">{{ $salary->periode }}</div>
                                            <small class="text-muted">
                                                Dibuat: {{ $salary->tanggal_dibuat->format('d/m/Y') }}
                                            </small>
                                        </td>
                                        <td>Rp {{ number_format($salary->gaji_pokok, 0, ',', '.') }}</td>
                                        <td>
                                            <div>{{ $salary->total_jam_lembur }} jam</div>
                                            <small class="text-success">Rp {{ number_format($salary->total_upah_lembur, 0, ',', '.') }}</small>
                                        </td>
                                        <td>
                                            <span class="text-danger">Rp {{ number_format($salary->total_potongan, 0, ',', '.') }}</span>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-primary">Rp {{ number_format($salary->gaji_bersih, 0, ',', '.') }}</span>
                                        </td>
                                        <td>
                                            <span class="badge {{ $salary->status_badge }}">{{ $salary->status_text }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('salary.show', $salary->id) }}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                @if($salary->status != 'draft')
                                                    <a href="{{ route('salary.print', $salary->id) }}" 
                                                       class="btn btn-sm btn-outline-secondary">
                                                        <i class="bi bi-printer"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="bi bi-inbox display-4"></i>
                                                <p class="mt-2">Belum ada slip gaji</p>
                                                <small>Slip gaji akan muncul setelah admin memproses gaji bulanan</small>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($salaries->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $salaries->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Info Cards -->
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-clock-history display-4 text-warning"></i>
                    <h5 class="mt-3">Riwayat Lembur</h5>
                    <p class="text-muted">Lihat detail jam lembur Anda</p>
                    <a href="{{ route('salary.overtime') }}" class="btn btn-warning">
                        <i class="bi bi-eye me-1"></i>
                        Lihat Riwayat
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-dash-circle display-4 text-danger"></i>
                    <h5 class="mt-3">Riwayat Potongan</h5>
                    <p class="text-muted">Lihat detail potongan gaji Anda</p>
                    <a href="{{ route('salary.deductions') }}" class="btn btn-danger">
                        <i class="bi bi-eye me-1"></i>
                        Lihat Riwayat
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-person-gear display-4 text-info"></i>
                    <h5 class="mt-3">Profil Gaji</h5>
                    <p class="text-muted">Lihat informasi gaji Anda</p>
                    <a href="{{ route('profile.show') }}" class="btn btn-info">
                        <i class="bi bi-eye me-1"></i>
                        Lihat Profil
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
