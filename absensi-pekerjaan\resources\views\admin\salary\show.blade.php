@extends('layouts.admin')

@section('title', 'Detail Slip Gaji')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-receipt me-2"></i>
                            Detail Slip Gaji - {{ $salary->user->name }}
                        </h5>
                        <div>
                            <a href="{{ route('admin.salary.index') }}" class="btn btn-light btn-sm me-2">
                                <i class="bi bi-arrow-left me-1"></i>
                                Kembali
                            </a>
                            <a href="{{ route('admin.salary.print', $salary->id) }}" class="btn btn-success btn-sm">
                                <i class="bi bi-printer me-1"></i>
                                Cetak PDF
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- Info <PERSON>wan -->
                        <div class="col-md-4">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    @if($salary->user->foto_profil)
                                        <img src="{{ asset('storage/' . $salary->user->foto_profil) }}" 
                                             class="rounded-circle mb-3" width="80" height="80">
                                    @else
                                        <div class="bg-secondary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" 
                                             style="width: 80px; height: 80px;">
                                            <i class="bi bi-person text-white fs-2"></i>
                                        </div>
                                    @endif
                                    <h6 class="fw-bold">{{ $salary->user->name }}</h6>
                                    <p class="text-muted mb-1">{{ $salary->user->jabatan }}</p>
                                    <p class="text-muted mb-1">NIK: {{ $salary->user->nik }}</p>
                                    <span class="badge {{ $salary->status_badge }}">{{ $salary->status_text }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Detail Gaji -->
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="fw-bold text-success mb-3">
                                        <i class="bi bi-plus-circle me-2"></i>
                                        Pendapatan
                                    </h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td>Gaji Pokok</td>
                                            <td class="text-end">Rp {{ number_format($salary->gaji_pokok, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Tunjangan Transport</td>
                                            <td class="text-end">Rp {{ number_format($salary->tunjangan_transport, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Tunjangan Makan</td>
                                            <td class="text-end">Rp {{ number_format($salary->tunjangan_makan, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Tunjangan Lainnya</td>
                                            <td class="text-end">Rp {{ number_format($salary->tunjangan_lainnya, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Lembur ({{ $salary->total_jam_lembur }} jam)</td>
                                            <td class="text-end">Rp {{ number_format($salary->total_upah_lembur, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr class="table-success fw-bold">
                                            <td>Total Pendapatan</td>
                                            <td class="text-end">Rp {{ number_format($salary->total_pendapatan, 0, ',', '.') }}</td>
                                        </tr>
                                    </table>
                                </div>

                                <div class="col-md-6">
                                    <h6 class="fw-bold text-danger mb-3">
                                        <i class="bi bi-dash-circle me-2"></i>
                                        Potongan
                                    </h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td>Keterlambatan</td>
                                            <td class="text-end">Rp {{ number_format($salary->potongan_terlambat, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Alpha</td>
                                            <td class="text-end">Rp {{ number_format($salary->potongan_alpha, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Surat Peringatan</td>
                                            <td class="text-end">Rp {{ number_format($salary->potongan_sp, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Lainnya</td>
                                            <td class="text-end">Rp {{ number_format($salary->potongan_lainnya, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr class="table-danger fw-bold">
                                            <td>Total Potongan</td>
                                            <td class="text-end">Rp {{ number_format($salary->total_potongan, 0, ',', '.') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Gaji Bersih -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h4 class="mb-0">Gaji Bersih</h4>
                                            <h2 class="fw-bold">Rp {{ number_format($salary->gaji_bersih, 0, ',', '.') }}</h2>
                                            <small>Periode: {{ $salary->periode }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Kehadiran -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">
                                <i class="bi bi-calendar-check me-2"></i>
                                Data Kehadiran
                            </h6>
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="card border-success">
                                        <div class="card-body">
                                            <h4 class="text-success">{{ $salary->total_hadir }}</h4>
                                            <small>Hadir</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <h4 class="text-warning">{{ $salary->total_terlambat }}</h4>
                                            <small>Terlambat</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="card border-danger">
                                        <div class="card-body">
                                            <h4 class="text-danger">{{ $salary->total_alpha }}</h4>
                                            <small>Alpha</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row text-center mt-2">
                                <div class="col-4">
                                    <div class="card border-info">
                                        <div class="card-body">
                                            <h4 class="text-info">{{ $salary->total_izin }}</h4>
                                            <small>Izin</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="card border-secondary">
                                        <div class="card-body">
                                            <h4 class="text-secondary">{{ $salary->total_sakit }}</h4>
                                            <small>Sakit</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="card border-primary">
                                        <div class="card-body">
                                            <h4 class="text-primary">{{ $salary->total_cuti }}</h4>
                                            <small>Cuti</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Aksi -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">
                                <i class="bi bi-gear me-2"></i>
                                Aksi
                            </h6>
                            @if($salary->status == 'draft')
                                <form method="POST" action="{{ route('admin.salary.approve', $salary->id) }}" class="mb-2">
                                    @csrf
                                    <button type="submit" class="btn btn-success w-100" 
                                            onclick="return confirm('Setujui slip gaji ini?')">
                                        <i class="bi bi-check-circle me-2"></i>
                                        Setujui Slip Gaji
                                    </button>
                                </form>
                            @endif
                            @if($salary->status == 'approved')
                                <form method="POST" action="{{ route('admin.salary.mark-paid', $salary->id) }}" class="mb-2">
                                    @csrf
                                    <button type="submit" class="btn btn-primary w-100" 
                                            onclick="return confirm('Tandai sebagai dibayar?')">
                                        <i class="bi bi-cash me-2"></i>
                                        Tandai Sebagai Dibayar
                                    </button>
                                </form>
                            @endif
                            
                            <!-- Info Approval -->
                            @if($salary->status != 'draft')
                                <div class="card bg-light mt-3">
                                    <div class="card-body">
                                        <h6 class="card-title">Informasi Approval</h6>
                                        @if($salary->approver)
                                            <p class="mb-1"><strong>Disetujui oleh:</strong> {{ $salary->approver->name }}</p>
                                        @endif
                                        @if($salary->tanggal_disetujui)
                                            <p class="mb-1"><strong>Tanggal Disetujui:</strong> {{ $salary->tanggal_disetujui->format('d/m/Y H:i') }}</p>
                                        @endif
                                        @if($salary->tanggal_dibayar)
                                            <p class="mb-0"><strong>Tanggal Dibayar:</strong> {{ $salary->tanggal_dibayar->format('d/m/Y H:i') }}</p>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
