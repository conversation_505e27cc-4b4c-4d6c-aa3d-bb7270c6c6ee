<div class="row">
    <div class="col-md-6">
        <div class="mb-3">
            <label class="form-label fw-bold">
                <i class="bi bi-person me-2 text-primary"></i><PERSON><PERSON><PERSON>
            </label>
            <select name="user_id" class="form-select" required>
                <option value=""><PERSON><PERSON><PERSON></option>
                @foreach($users as $user)
                    <option value="{{ $user->id }}" {{ $deduction->user_id == $user->id ? 'selected' : '' }}>
                        {{ $user->name }} - {{ $user->jabatan ?? 'Karyawan' }}
                    </option>
                @endforeach
            </select>
        </div>
        
        <div class="mb-3">
            <label class="form-label fw-bold">
                <i class="bi bi-calendar3 me-2 text-primary"></i>Tanggal
            </label>
            <input type="date" name="tanggal" class="form-control" 
                   value="{{ $deduction->tanggal->format('Y-m-d') }}" required>
        </div>
        
        <div class="mb-3">
            <label class="form-label fw-bold">
                <i class="bi bi-tag me-2 text-primary"></i><PERSON><PERSON>
            </label>
            <select name="jenis" class="form-select" required>
                <option value="alpha" {{ $deduction->jenis == 'alpha' ? 'selected' : '' }}>Alpha</option>
                <option value="terlambat" {{ $deduction->jenis == 'terlambat' ? 'selected' : '' }}>Terlambat</option>
                <option value="sp1" {{ $deduction->jenis == 'sp1' ? 'selected' : '' }}>SP 1</option>
                <option value="sp2" {{ $deduction->jenis == 'sp2' ? 'selected' : '' }}>SP 2</option>
                <option value="sp3" {{ $deduction->jenis == 'sp3' ? 'selected' : '' }}>SP 3</option>
                <option value="lainnya" {{ $deduction->jenis == 'lainnya' ? 'selected' : '' }}>Lainnya</option>
            </select>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="mb-3">
            <label class="form-label fw-bold">
                <i class="bi bi-currency-dollar me-2 text-primary"></i>Jumlah Potongan (Rp)
            </label>
            <input type="number" name="jumlah_potongan" class="form-control" 
                   min="0" step="1000" required placeholder="0"
                   value="{{ $deduction->jumlah_potongan }}">
        </div>
        
        <div class="mb-3">
            <label class="form-label fw-bold">
                <i class="bi bi-chat-text me-2 text-primary"></i>Keterangan
            </label>
            <textarea name="keterangan" class="form-control" rows="4" required
                      placeholder="Alasan potongan gaji...">{{ $deduction->keterangan }}</textarea>
        </div>
    </div>
</div>

<div class="alert alert-info border-0 mt-3">
    <div class="d-flex align-items-center">
        <i class="bi bi-info-circle-fill me-3"></i>
        <div>
            <strong>Informasi:</strong> Perubahan akan mempengaruhi perhitungan gaji karyawan untuk periode {{ \DateTime::createFromFormat('!m', $deduction->bulan)->format('F') }} {{ $deduction->tahun }}.
        </div>
    </div>
</div>

@if($deduction->absensi_id)
    <div class="alert alert-warning border-0 mt-3">
        <div class="d-flex align-items-center">
            <i class="bi bi-exclamation-triangle-fill me-3"></i>
            <div>
                <strong>Perhatian:</strong> Potongan ini terkait dengan data absensi. Perubahan harus dilakukan dengan hati-hati.
                <a href="{{ route('admin.approval.show', $deduction->absensi_id) }}" class="alert-link">Lihat data absensi terkait</a>
            </div>
        </div>
    </div>
@endif
