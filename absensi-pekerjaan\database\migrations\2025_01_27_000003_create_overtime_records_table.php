<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('overtime_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('absensi_id')->constrained('absensis')->onDelete('cascade');
            $table->date('tanggal');
            
            // Waktu lembur
            $table->time('jam_pulang_normal'); // Jam pulang seharusnya
            $table->time('jam_keluar_aktual'); // Jam keluar sebenarnya
            $table->decimal('jam_lembur', 8, 2); // Jumlah jam lembur (dalam desimal)
            
            // Perhitungan upah
            $table->decimal('upah_per_jam', 15, 2); // Upah per jam normal
            $table->decimal('rate_lembur', 8, 2)->default(1.5); // Rate lembur (misal 1.5x)
            $table->decimal('upah_lembur_per_jam', 15, 2); // Upah lembur per jam
            $table->decimal('total_upah_lembur', 15, 2); // Total upah lembur
            
            // Status dan approval
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('keterangan')->nullable();
            $table->text('catatan_admin')->nullable();
            $table->timestamp('tanggal_disetujui')->nullable();
            $table->unsignedBigInteger('disetujui_oleh')->nullable();
            
            $table->timestamps();
            
            // Index
            $table->index(['user_id', 'tanggal']);
            $table->index(['tanggal', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('overtime_records');
    }
};
