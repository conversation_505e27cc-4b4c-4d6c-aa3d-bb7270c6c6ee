<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class OvertimeRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'absensi_id',
        'tanggal',
        'jam_pulang_normal',
        'jam_keluar_aktual',
        'jam_lembur',
        'upah_per_jam',
        'rate_lembur',
        'upah_lembur_per_jam',
        'total_upah_lembur',
        'status',
        'keterangan',
        'catatan_admin',
        'tanggal_disetujui',
        'disetujui_oleh'
    ];

    protected $casts = [
        'tanggal' => 'date',
        'tanggal_disetujui' => 'datetime',
        'jam_lembur' => 'decimal:2',
        'upah_per_jam' => 'decimal:2',
        'rate_lembur' => 'decimal:2',
        'upah_lembur_per_jam' => 'decimal:2',
        'total_upah_lembur' => 'decimal:2',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function absensi()
    {
        return $this->belongsTo(Absensi::class);
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'disetujui_oleh');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => 'bg-warning',
            'approved' => 'bg-success',
            'rejected' => 'bg-danger'
        ];

        return $badges[$this->status] ?? 'bg-secondary';
    }

    public function getStatusTextAttribute()
    {
        $texts = [
            'pending' => 'Menunggu',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak'
        ];

        return $texts[$this->status] ?? 'Menunggu';
    }

    public function getJamLemburFormatAttribute()
    {
        $hours = floor($this->jam_lembur);
        $minutes = ($this->jam_lembur - $hours) * 60;

        if ($hours > 0 && $minutes > 0) {
            return $hours . ' jam ' . round($minutes) . ' menit';
        } elseif ($hours > 0) {
            return $hours . ' jam';
        } else {
            return round($minutes) . ' menit';
        }
    }

    // Methods
    public static function calculateOvertimeFromAbsensi($absensi)
    {
        if (!$absensi->jam_keluar) {
            return null;
        }

        // Ambil jam pulang normal dari setting
        $jamPulangNormal = Setting::getValue('jam_pulang', '17:00:00');

        // Parse waktu dengan tanggal yang sama untuk perhitungan yang benar
        $tanggal = $absensi->tanggal;
        $jamPulang = Carbon::createFromFormat('Y-m-d H:i:s', $tanggal . ' ' . $jamPulangNormal);
        $jamKeluar = Carbon::createFromFormat('Y-m-d H:i:s', $tanggal . ' ' . $absensi->jam_keluar);

        // Hitung lembur jika jam keluar lebih dari jam pulang normal
        if ($jamKeluar->gt($jamPulang)) {
            $jamLembur = $jamPulang->diffInMinutes($jamKeluar) / 60; // Konversi ke jam desimal

            // Hitung upah per jam dari gaji pokok
            $user = $absensi->user;
            $gajiPokok = $user->gaji_pokok ?? Setting::getValue('gaji_pokok_default', 3000000);
            $upahPerJam = $gajiPokok / (22 * 8); // 22 hari kerja, 8 jam per hari

            // Rate lembur dari setting
            $rateLembur = (float)Setting::getValue('rate_lembur', 1.5);
            $upahLemburPerJam = $upahPerJam * $rateLembur;
            $totalUpahLembur = $upahLemburPerJam * $jamLembur;

            return [
                'jam_pulang_normal' => $jamPulangNormal,
                'jam_keluar_aktual' => $absensi->jam_keluar,
                'jam_lembur' => $jamLembur,
                'upah_per_jam' => $upahPerJam,
                'rate_lembur' => $rateLembur,
                'upah_lembur_per_jam' => $upahLemburPerJam,
                'total_upah_lembur' => $totalUpahLembur
            ];
        }

        return null;
    }

    // Scopes
    public function scopeByPeriod($query, $bulan, $tahun)
    {
        return $query->whereMonth('tanggal', $bulan)->whereYear('tanggal', $tahun);
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }
}
