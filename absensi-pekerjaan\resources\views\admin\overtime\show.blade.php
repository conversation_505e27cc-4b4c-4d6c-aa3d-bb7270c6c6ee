@extends('layouts.admin')

@section('title', 'Detail Lembur')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-warning text-dark">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-clock me-2"></i>
                            Detail Lembur - {{ $overtime->user->name }}
                        </h5>
                        <a href="{{ route('admin.overtime.index') }}" class="btn btn-dark btn-sm">
                            <i class="bi bi-arrow-left me-1"></i>
                            Kembali
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- Info Karyawan -->
                        <div class="col-md-4">
                            <div class="card border-0 bg-light">
                                <div class="card-body text-center">
                                    @if($overtime->user->foto_profil)
                                        <img src="{{ asset('storage/' . $overtime->user->foto_profil) }}" 
                                             class="rounded-circle mb-3" width="80" height="80">
                                    @else
                                        <div class="bg-secondary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" 
                                             style="width: 80px; height: 80px;">
                                            <i class="bi bi-person text-white fs-2"></i>
                                        </div>
                                    @endif
                                    <h6 class="fw-bold">{{ $overtime->user->name }}</h6>
                                    <p class="text-muted mb-1">{{ $overtime->user->jabatan }}</p>
                                    <p class="text-muted mb-1">NIK: {{ $overtime->user->nik }}</p>
                                    <span class="badge {{ $overtime->status_badge }}">{{ $overtime->status_text }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Detail Lembur -->
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="fw-bold text-info mb-3">
                                        <i class="bi bi-calendar-event me-2"></i>
                                        Informasi Waktu
                                    </h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td>Tanggal</td>
                                            <td class="text-end fw-bold">{{ $overtime->tanggal->format('d/m/Y') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Jam Pulang Normal</td>
                                            <td class="text-end">{{ \Carbon\Carbon::parse($overtime->jam_pulang_normal)->format('H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Jam Keluar Aktual</td>
                                            <td class="text-end">{{ \Carbon\Carbon::parse($overtime->jam_keluar_aktual)->format('H:i') }}</td>
                                        </tr>
                                        <tr class="table-warning">
                                            <td>Total Jam Lembur</td>
                                            <td class="text-end fw-bold">{{ $overtime->jam_lembur_format }}</td>
                                        </tr>
                                    </table>
                                </div>

                                <div class="col-md-6">
                                    <h6 class="fw-bold text-success mb-3">
                                        <i class="bi bi-cash me-2"></i>
                                        Perhitungan Upah
                                    </h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td>Upah Per Jam Normal</td>
                                            <td class="text-end">Rp {{ number_format($overtime->upah_per_jam, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Rate Lembur</td>
                                            <td class="text-end">{{ $overtime->rate_lembur }}x</td>
                                        </tr>
                                        <tr>
                                            <td>Upah Lembur Per Jam</td>
                                            <td class="text-end">Rp {{ number_format($overtime->upah_lembur_per_jam, 0, ',', '.') }}</td>
                                        </tr>
                                        <tr class="table-success fw-bold">
                                            <td>Total Upah Lembur</td>
                                            <td class="text-end">Rp {{ number_format($overtime->total_upah_lembur, 0, ',', '.') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Keterangan -->
                            @if($overtime->keterangan)
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h6 class="fw-bold text-secondary mb-3">
                                            <i class="bi bi-chat-text me-2"></i>
                                            Keterangan
                                        </h6>
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                {{ $overtime->keterangan }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Catatan Admin -->
                            @if($overtime->catatan_admin)
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h6 class="fw-bold text-danger mb-3">
                                            <i class="bi bi-exclamation-triangle me-2"></i>
                                            Catatan Admin
                                        </h6>
                                        <div class="card border-danger">
                                            <div class="card-body">
                                                {{ $overtime->catatan_admin }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Aksi -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h6 class="fw-bold mb-3">
                                        <i class="bi bi-gear me-2"></i>
                                        Aksi
                                    </h6>
                                    @if($overtime->status == 'pending')
                                        <div class="d-flex gap-2">
                                            <form method="POST" action="{{ route('admin.overtime.approve', $overtime->id) }}" class="d-inline">
                                                @csrf
                                                <div class="input-group">
                                                    <input type="text" name="catatan_admin" class="form-control" 
                                                           placeholder="Catatan (opsional)">
                                                    <button type="submit" class="btn btn-success" 
                                                            onclick="return confirm('Setujui lembur ini?')">
                                                        <i class="bi bi-check-circle me-1"></i>
                                                        Setujui
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                        <div class="mt-2">
                                            <form method="POST" action="{{ route('admin.overtime.reject', $overtime->id) }}" class="d-inline">
                                                @csrf
                                                <div class="input-group">
                                                    <input type="text" name="catatan_admin" class="form-control" 
                                                           placeholder="Alasan penolakan (wajib)" required>
                                                    <button type="submit" class="btn btn-danger" 
                                                            onclick="return confirm('Tolak lembur ini?')">
                                                        <i class="bi bi-x-circle me-1"></i>
                                                        Tolak
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    @endif
                                    
                                    <!-- Info Approval -->
                                    @if($overtime->status != 'pending')
                                        <div class="card bg-light mt-3">
                                            <div class="card-body">
                                                <h6 class="card-title">Informasi Approval</h6>
                                                @if($overtime->approver)
                                                    <p class="mb-1"><strong>Diproses oleh:</strong> {{ $overtime->approver->name }}</p>
                                                @endif
                                                @if($overtime->tanggal_disetujui)
                                                    <p class="mb-0"><strong>Tanggal Diproses:</strong> {{ $overtime->tanggal_disetujui->format('d/m/Y H:i') }}</p>
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Info Absensi Terkait -->
                    @if($overtime->absensi)
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="bi bi-calendar-check me-2"></i>
                                            Data Absensi Terkait
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <strong>Jam Masuk:</strong><br>
                                                {{ $overtime->absensi->jam_masuk ? \Carbon\Carbon::parse($overtime->absensi->jam_masuk)->format('H:i') : '-' }}
                                            </div>
                                            <div class="col-md-3">
                                                <strong>Jam Keluar:</strong><br>
                                                {{ $overtime->absensi->jam_keluar ? \Carbon\Carbon::parse($overtime->absensi->jam_keluar)->format('H:i') : '-' }}
                                            </div>
                                            <div class="col-md-3">
                                                <strong>Status:</strong><br>
                                                <span class="badge bg-success">{{ ucfirst($overtime->absensi->status) }}</span>
                                            </div>
                                            <div class="col-md-3">
                                                <strong>Keterangan:</strong><br>
                                                {{ $overtime->absensi->keterangan ?? '-' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
