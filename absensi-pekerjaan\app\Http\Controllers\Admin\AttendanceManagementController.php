<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Absensi;
use App\Models\SalaryDeduction;
use App\Models\Setting;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AttendanceManagementController extends Controller
{
    public function index(Request $request)
    {
        $tanggal = $request->get('tanggal', date('Y-m-d'));
        $status = $request->get('status');

        $query = Absensi::with('user')
            ->whereDate('tanggal', $tanggal);

        if ($status) {
            $query->where('status', $status);
        }

        $absensis = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get users who don't have attendance record for this date
        $usersWithoutAttendance = User::where('role', 'user')
            ->whereNotIn('id', function($query) use ($tanggal) {
                $query->select('user_id')
                      ->from('absensis')
                      ->whereDate('tanggal', $tanggal);
            })
            ->get();

        return view('admin.attendance.index', compact('absensis', 'usersWithoutAttendance', 'tanggal', 'status'));
    }

    public function markAsAlpha(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'tanggal' => 'required|date',
            'keterangan' => 'nullable|string|max:500'
        ]);

        $tanggal = Carbon::parse($request->tanggal);
        $markedCount = 0;

        foreach ($request->user_ids as $userId) {
            $user = User::find($userId);

            // Cek apakah sudah ada record untuk tanggal ini
            $existing = Absensi::where('user_id', $userId)
                ->whereDate('tanggal', $tanggal->format('Y-m-d'))
                ->first();

            if (!$existing) {
                // Cek apakah user memiliki izin/cuti/sakit yang disetujui untuk tanggal ini
                $hasApprovedLeave = $this->hasApprovedLeaveForDate($user, $tanggal);

                if ($hasApprovedLeave) {
                    // Skip marking as alpha if user has approved leave
                    continue;
                }

                // Buat record alpha
                $absensi = Absensi::create([
                    'user_id' => $userId,
                    'tanggal' => $tanggal->format('Y-m-d'),
                    'jam_masuk' => null,
                    'jam_keluar' => null,
                    'keterangan' => $request->keterangan ?? 'Ditandai alpha oleh admin',
                    'status' => 'alpha',
                    'status_approval' => 'approved',
                    'lokasi_masuk' => 'Admin Generated',
                    'lokasi_keluar' => 'Admin Generated',
                    'foto_masuk' => null,
                    'foto_keluar' => null,
                    'tanda_tangan' => null,
                    'catatan' => 'Ditandai alpha oleh admin: ' . auth()->user()->name,
                    'approval_at' => now(),
                    'approved_by' => auth()->id()
                ]);

                // Buat potongan gaji
                $this->createAlphaDeduction($user, $tanggal, $absensi);
                $markedCount++;
            }
        }

        return redirect()->back()->with('success', "Berhasil menandai {$markedCount} karyawan sebagai alpha");
    }

    public function processDaily(Request $request)
    {
        $request->validate([
            'tanggal' => 'required|date'
        ]);

        $tanggal = Carbon::parse($request->tanggal);

        // Run attendance check command
        \Artisan::call('attendance:check-daily', ['date' => $tanggal->format('Y-m-d')]);
        $attendanceOutput = \Artisan::output();

        // Run lateness processing command
        \Artisan::call('attendance:process-lateness', ['date' => $tanggal->format('Y-m-d')]);
        $latenessOutput = \Artisan::output();

        return redirect()->back()->with('success', 'Proses harian berhasil dijalankan')
                                ->with('command_output', $attendanceOutput . "\n" . $latenessOutput);
    }

    public function bulkProcessMonth(Request $request)
    {
        $request->validate([
            'bulan' => 'required|integer|min:1|max:12',
            'tahun' => 'required|integer|min:2020|max:2030'
        ]);

        $bulan = $request->bulan;
        $tahun = $request->tahun;

        $startDate = Carbon::create($tahun, $bulan, 1);
        $endDate = $startDate->copy()->endOfMonth();

        $processedDays = 0;
        $output = [];

        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            // Skip weekend if configured
            $skipWeekend = Setting::getValue('skip_weekend_attendance', 'true');
            if ($skipWeekend === 'true' && $date->isWeekend()) {
                continue;
            }

            // Run daily processing
            \Artisan::call('attendance:check-daily', ['date' => $date->format('Y-m-d')]);
            \Artisan::call('attendance:process-lateness', ['date' => $date->format('Y-m-d')]);

            $processedDays++;
            $output[] = "Processed: " . $date->format('Y-m-d');
        }

        return redirect()->back()->with('success', "Berhasil memproses {$processedDays} hari untuk periode {$bulan}/{$tahun}")
                                ->with('command_output', implode("\n", $output));
    }

    public function deductions(Request $request)
    {
        $bulan = $request->get('bulan', date('n'));
        $tahun = $request->get('tahun', date('Y'));
        $jenis = $request->get('jenis');
        $userId = $request->get('user_id');

        $query = SalaryDeduction::with(['user', 'absensi'])
            ->where('bulan', $bulan)
            ->where('tahun', $tahun);

        if ($jenis) {
            $query->where('jenis', $jenis);
        }

        if ($userId) {
            $query->where('user_id', $userId);
        }

        $deductions = $query->orderBy('tanggal', 'desc')->paginate(20);

        // Check for orphaned deductions (potongan yang tidak sinkron dengan absensi)
        $this->checkAndCleanOrphanedDeductions($bulan, $tahun);

        // Statistik untuk cards
        $allDeductions = SalaryDeduction::where('bulan', $bulan)
            ->where('tahun', $tahun)
            ->get();

        $stats = [
            'total_amount' => $allDeductions->sum('jumlah_potongan'),
            'total_count' => $allDeductions->count(),
            'alpha_count' => $allDeductions->where('jenis', 'alpha')->count(),
            'alpha_amount' => $allDeductions->where('jenis', 'alpha')->sum('jumlah_potongan'),
            'terlambat_count' => $allDeductions->where('jenis', 'terlambat')->count(),
            'terlambat_amount' => $allDeductions->where('jenis', 'terlambat')->sum('jumlah_potongan'),
            'sp_count' => $allDeductions->whereIn('jenis', ['sp1', 'sp2', 'sp3'])->count(),
            'sp_amount' => $allDeductions->whereIn('jenis', ['sp1', 'sp2', 'sp3'])->sum('jumlah_potongan'),
        ];

        // Daftar user untuk filter
        $users = \App\Models\User::where('role', 'user')->orderBy('name')->get();

        // Check if export is requested
        if ($request->has('export') && $request->export === 'pdf') {
            return $this->exportDeductionsPDF($request, $bulan, $tahun, $jenis, $userId, $stats);
        }

        return view('admin.attendance.deductions', compact('deductions', 'bulan', 'tahun', 'jenis', 'userId', 'stats', 'users'));
    }

    public function cutiStats(Request $request)
    {
        $tahun = $request->get('tahun', date('Y'));
        $userId = $request->get('user_id');
        $export = $request->get('export');

        $query = \App\Models\User::where('role', 'user');

        if ($userId) {
            $query->where('id', $userId);
        }

        $users = $query->get();
        $cutiStats = [];

        foreach ($users as $user) {
            $cutiTerpakai = \App\Models\Absensi::where('user_id', $user->id)
                ->where('status', 'cuti')
                ->where('status_approval', 'approved')
                ->whereYear('tanggal', $tahun)
                ->count();

            $sisaCuti = 12 - $cutiTerpakai;

            $cutiStats[] = [
                'user' => $user,
                'cuti_terpakai' => $cutiTerpakai,
                'sisa_cuti' => $sisaCuti,
                'status' => $sisaCuti > 0 ? 'available' : 'exceeded'
            ];
        }

        // Handle PDF export
        if ($export === 'pdf') {
            return $this->exportCutiStatsPDF($cutiStats, $tahun, $userId);
        }

        return view('admin.attendance.cuti-stats', compact('cutiStats', 'tahun', 'userId'));
    }

    private function exportCutiStatsPDF($cutiStats, $tahun, $userId)
    {
        try {
            // Company settings
            $namaPerusahaan = Setting::getValue('nama_perusahaan', 'PT. Absensi Indonesia');
            $alamatPerusahaan = Setting::getValue('alamat_perusahaan', 'Jl. Contoh No. 123, Jakarta');

            // Statistics
            $totalKaryawan = count($cutiStats);
            $karyawanMelebihi = collect($cutiStats)->where('status', 'exceeded')->count();
            $totalCutiTerpakai = collect($cutiStats)->sum('cuti_terpakai');
            $totalSisaCuti = collect($cutiStats)->sum('sisa_cuti');

            // Filter info
            $filterInfo = '';
            if ($userId) {
                $user = User::find($userId);
                $filterInfo = 'Karyawan: ' . ($user ? $user->name : 'Tidak ditemukan');
            } else {
                $filterInfo = 'Semua Karyawan';
            }

            // Create PDF
            $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('admin.attendance.cuti_stats_pdf', compact(
                'cutiStats',
                'tahun',
                'namaPerusahaan',
                'alamatPerusahaan',
                'totalKaryawan',
                'karyawanMelebihi',
                'totalCutiTerpakai',
                'totalSisaCuti',
                'filterInfo'
            ));

            $pdf->setPaper('a4', 'portrait');

            $filename = 'Monitor_Cuti_Karyawan_' . $tahun . '.pdf';

            return $pdf->download($filename);
        } catch (\Exception $e) {
            \Log::error('Error export cuti stats PDF: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Gagal mengekspor PDF: ' . $e->getMessage());
        }
    }

    public function deleteDeduction($id)
    {
        $deduction = SalaryDeduction::findOrFail($id);
        $deduction->delete();

        return redirect()->back()->with('success', 'Potongan gaji berhasil dihapus');
    }

    public function createManualDeduction(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'tanggal' => 'required|date',
            'jenis' => 'required|in:terlambat,alpha,sp1,sp2,sp3,lainnya',
            'jumlah_potongan' => 'required|numeric|min:0',
            'keterangan' => 'required|string|max:500'
        ]);

        $tanggal = Carbon::parse($request->tanggal);

        SalaryDeduction::create([
            'user_id' => $request->user_id,
            'tanggal' => $tanggal->format('Y-m-d'),
            'bulan' => $tanggal->month,
            'tahun' => $tanggal->year,
            'jenis' => $request->jenis,
            'jumlah_potongan' => $request->jumlah_potongan,
            'keterangan' => $request->keterangan,
            'status' => 'approved',
            'dibuat_oleh' => auth()->id(),
            'disetujui_oleh' => auth()->id()
        ]);

        return redirect()->back()->with('success', 'Potongan gaji manual berhasil dibuat');
    }

    public function viewDeduction($id)
    {
        $deduction = SalaryDeduction::with(['user', 'absensi', 'creator', 'approver'])->findOrFail($id);

        return view('admin.attendance.deduction_view', compact('deduction'));
    }

    public function editDeduction($id)
    {
        $deduction = SalaryDeduction::findOrFail($id);
        $users = \App\Models\User::where('role', 'user')->orderBy('name')->get();

        return view('admin.attendance.deduction_edit', compact('deduction', 'users'));
    }

    public function updateDeduction(Request $request, $id)
    {
        $deduction = SalaryDeduction::findOrFail($id);

        $request->validate([
            'user_id' => 'required|exists:users,id',
            'tanggal' => 'required|date',
            'jenis' => 'required|in:terlambat,alpha,sp1,sp2,sp3,lainnya',
            'jumlah_potongan' => 'required|numeric|min:0',
            'keterangan' => 'required|string|max:500'
        ]);

        $tanggal = Carbon::parse($request->tanggal);

        $deduction->update([
            'user_id' => $request->user_id,
            'tanggal' => $tanggal->format('Y-m-d'),
            'bulan' => $tanggal->month,
            'tahun' => $tanggal->year,
            'jenis' => $request->jenis,
            'jumlah_potongan' => $request->jumlah_potongan,
            'keterangan' => $request->keterangan,
        ]);

        return redirect()->route('admin.attendance.deductions')->with('success', 'Potongan gaji berhasil diperbarui');
    }

    public function approveDeduction($id)
    {
        $deduction = SalaryDeduction::findOrFail($id);

        $deduction->update([
            'status' => 'approved',
            'tanggal_disetujui' => now(),
            'disetujui_oleh' => auth()->id()
        ]);

        return redirect()->back()->with('success', 'Potongan gaji berhasil disetujui');
    }

    public function rejectDeduction(Request $request, $id)
    {
        $deduction = SalaryDeduction::findOrFail($id);

        $deduction->update([
            'status' => 'rejected',
            'catatan' => $request->reason,
            'tanggal_disetujui' => now(),
            'disetujui_oleh' => auth()->id()
        ]);

        return redirect()->back()->with('success', 'Potongan gaji berhasil ditolak');
    }

    public function attendanceDetail($id)
    {
        $absensi = Absensi::with(['user', 'salaryDeductions'])->findOrFail($id);

        return view('admin.attendance.detail', compact('absensi'));
    }

    public function attendanceEdit($id)
    {
        $absensi = Absensi::with(['user'])->findOrFail($id);

        return view('admin.attendance.edit', compact('absensi'));
    }

    public function attendanceUpdate(Request $request, $id)
    {
        $absensi = Absensi::findOrFail($id);
        $oldStatus = $absensi->status;

        $request->validate([
            'tanggal' => 'required|date',
            'status' => 'required|in:hadir,alpha,izin,sakit,cuti',
            'jam_masuk' => 'nullable|regex:/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/',
            'jam_keluar' => 'nullable|regex:/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/',
            'keterangan' => 'nullable|string|max:500'
        ], [
            'tanggal.required' => 'Tanggal harus diisi.',
            'tanggal.date' => 'Format tanggal tidak valid.',
            'status.required' => 'Status absensi harus dipilih.',
            'status.in' => 'Status absensi tidak valid.',
            'jam_masuk.regex' => 'Format jam masuk harus HH:MM (contoh: 08:30).',
            'jam_keluar.regex' => 'Format jam keluar harus HH:MM (contoh: 17:30).',
            'keterangan.max' => 'Keterangan maksimal 500 karakter.',
        ]);

        $absensi->update([
            'tanggal' => $request->tanggal,
            'status' => $request->status,
            'jam_masuk' => $request->jam_masuk ? $request->tanggal . ' ' . $request->jam_masuk . ':00' : null,
            'jam_keluar' => $request->jam_keluar ? $request->tanggal . ' ' . $request->jam_keluar . ':00' : null,
            'keterangan' => $request->keterangan,
        ]);

        // Handle salary deduction changes based on status change
        $deductionChanges = $this->handleStatusChangeDeductions($absensi, $oldStatus, $request->status);

        $message = 'Data absensi berhasil diperbarui';
        if (!empty($deductionChanges)) {
            $message .= '. ' . $deductionChanges;
        }

        return redirect()->route('admin.attendance.index')->with('success', $message);
    }

    public function attendanceDelete($id)
    {
        $absensi = Absensi::findOrFail($id);

        // Hapus juga salary deduction yang terkait jika ada
        SalaryDeduction::where('absensi_id', $id)->delete();

        $absensi->delete();

        return redirect()->back()->with('success', 'Data absensi berhasil dihapus');
    }

    private function exportDeductionsPDF($request, $bulan, $tahun, $jenis, $userId, $stats)
    {
        try {
            // Get all deductions for PDF (without pagination)
            $query = SalaryDeduction::with(['user', 'absensi'])
                ->where('bulan', $bulan)
                ->where('tahun', $tahun);

            if ($jenis) {
                $query->where('jenis', $jenis);
            }

            if ($userId) {
                $query->where('user_id', $userId);
            }

            $allDeductions = $query->orderBy('tanggal', 'desc')->get();

            // Company settings
            $namaPerusahaan = Setting::getValue('nama_perusahaan', 'PT. Absensi Indonesia');
            $alamatPerusahaan = Setting::getValue('alamat_perusahaan', 'Jl. Contoh No. 123, Jakarta');

            // Period info
            $periodeName = \DateTime::createFromFormat('!m', $bulan)->format('F') . ' ' . $tahun;

            // Create PDF
            $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('admin.attendance.deductions_pdf', compact(
                'allDeductions',
                'stats',
                'namaPerusahaan',
                'alamatPerusahaan',
                'periodeName',
                'bulan',
                'tahun'
            ));

            $pdf->setPaper('a4', 'landscape');

            $filename = 'Laporan_Potongan_Gaji_' . str_replace(' ', '_', $periodeName) . '.pdf';

            return $pdf->download($filename);
        } catch (\Exception $e) {
            \Log::error('Error export deductions PDF: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Gagal mengekspor PDF: ' . $e->getMessage());
        }
    }

    private function createAlphaDeduction($user, $date, $absensi)
    {
        $potonganAlpha = (float)Setting::getValue('potongan_alpha', 100000);

        SalaryDeduction::create([
            'user_id' => $user->id,
            'tanggal' => $date->format('Y-m-d'),
            'bulan' => $date->month,
            'tahun' => $date->year,
            'jenis' => 'alpha',
            'jumlah_potongan' => $potonganAlpha,
            'keterangan' => 'Potongan alpha/tanpa keterangan pada ' . $date->format('d/m/Y'),
            'absensi_id' => $absensi->id,
            'status' => 'approved',
            'dibuat_oleh' => auth()->id(),
            'disetujui_oleh' => auth()->id()
        ]);
    }

    private function handleStatusChangeDeductions($absensi, $oldStatus, $newStatus)
    {
        $message = '';

        // Jika status berubah dari alpha ke hadir, hapus potongan alpha
        if ($oldStatus == 'alpha' && $newStatus == 'hadir') {
            $deleted = SalaryDeduction::where('absensi_id', $absensi->id)
                ->where('jenis', 'alpha')
                ->delete();

            if ($deleted > 0) {
                $message = 'Potongan gaji alpha telah dihapus otomatis';
                \Log::info("Potongan alpha dihapus untuk absensi ID {$absensi->id} karena status berubah dari alpha ke hadir");
            }
        }

        // Jika status berubah dari hadir ke alpha, buat potongan alpha
        elseif ($oldStatus == 'hadir' && $newStatus == 'alpha') {
            $this->createAlphaDeduction($absensi->user, Carbon::parse($absensi->tanggal), $absensi);
            $message = 'Potongan gaji alpha telah dibuat otomatis';

            \Log::info("Potongan alpha dibuat untuk absensi ID {$absensi->id} karena status berubah dari hadir ke alpha");
        }

        // Jika status berubah dari terlambat ke hadir, hapus potongan terlambat
        elseif ($oldStatus == 'terlambat' && $newStatus == 'hadir') {
            $deleted = SalaryDeduction::where('absensi_id', $absensi->id)
                ->where('jenis', 'terlambat')
                ->delete();

            if ($deleted > 0) {
                $message = 'Potongan gaji terlambat telah dihapus otomatis';
                \Log::info("Potongan terlambat dihapus untuk absensi ID {$absensi->id} karena status berubah dari terlambat ke hadir");
            }
        }

        // Handle perubahan ke status yang tidak memerlukan potongan (izin, sakit, cuti)
        elseif (in_array($newStatus, ['izin', 'sakit', 'cuti']) && in_array($oldStatus, ['alpha', 'terlambat'])) {
            $deleted = SalaryDeduction::where('absensi_id', $absensi->id)
                ->whereIn('jenis', ['alpha', 'terlambat'])
                ->delete();

            if ($deleted > 0) {
                $message = "Potongan gaji {$oldStatus} telah dihapus otomatis karena status berubah ke {$newStatus}";
                \Log::info("Potongan {$oldStatus} dihapus untuk absensi ID {$absensi->id} karena status berubah ke {$newStatus}");
            }
        }

        return $message;
    }

    private function hasApprovedLeaveForDate($user, $date)
    {
        // Cek apakah user memiliki pengajuan izin/cuti/sakit yang disetujui untuk tanggal ini
        $approvedLeave = \App\Models\IzinCuti::where('user_id', $user->id)
            ->where('status', 'approved')
            ->where('tanggal_mulai', '<=', $date->format('Y-m-d'))
            ->where('tanggal_selesai', '>=', $date->format('Y-m-d'))
            ->first();

        return $approvedLeave !== null;
    }

    private function checkAndCleanOrphanedDeductions($bulan, $tahun)
    {
        // Cari potongan alpha yang absensinya sudah berubah status
        $orphanedAlpha = SalaryDeduction::with('absensi')
            ->where('bulan', $bulan)
            ->where('tahun', $tahun)
            ->where('jenis', 'alpha')
            ->whereHas('absensi', function($query) {
                $query->where('status', '!=', 'alpha');
            })
            ->get();

        // Cari potongan terlambat yang absensinya sudah berubah status
        $orphanedTerlambat = SalaryDeduction::with('absensi')
            ->where('bulan', $bulan)
            ->where('tahun', $tahun)
            ->where('jenis', 'terlambat')
            ->whereHas('absensi', function($query) {
                $query->whereNotIn('status', ['hadir', 'terlambat']);
            })
            ->get();

        $cleanedCount = 0;
        $cleanedDetails = [];

        // Hapus potongan alpha yang orphaned
        foreach ($orphanedAlpha as $deduction) {
            $cleanedDetails[] = "Alpha: {$deduction->user->name} ({$deduction->tanggal}) - Status absensi sekarang: {$deduction->absensi->status}";
            $deduction->delete();
            $cleanedCount++;
        }

        // Hapus potongan terlambat yang orphaned
        foreach ($orphanedTerlambat as $deduction) {
            $cleanedDetails[] = "Terlambat: {$deduction->user->name} ({$deduction->tanggal}) - Status absensi sekarang: {$deduction->absensi->status}";
            $deduction->delete();
            $cleanedCount++;
        }

        // Log jika ada yang dibersihkan
        if ($cleanedCount > 0) {
            \Log::info("Auto-cleaned {$cleanedCount} orphaned deductions for period {$bulan}/{$tahun}", $cleanedDetails);

            // Set flash message untuk admin
            session()->flash('info', "Sistem otomatis membersihkan {$cleanedCount} potongan gaji yang tidak sinkron dengan status absensi terbaru.");
        }
    }

    public function manualCleanOrphanedDeductions(Request $request)
    {
        $bulan = $request->get('bulan', date('n'));
        $tahun = $request->get('tahun', date('Y'));

        // Panggil method pembersihan
        $this->checkAndCleanOrphanedDeductions($bulan, $tahun);

        // Redirect kembali ke halaman deductions dengan parameter yang sama
        return redirect()->route('admin.attendance.deductions', $request->all())
            ->with('success', 'Pembersihan potongan gaji yang tidak sinkron telah selesai.');
    }

    public function cleanInvalidDeductions()
    {
        try {
            // Jalankan command untuk membersihkan potongan gaji yang tidak valid
            \Artisan::call('deductions:clean-invalid');
            $output = \Artisan::output();

            return redirect()->back()->with('success', 'Pembersihan potongan gaji tidak valid berhasil dijalankan.')
                                   ->with('command_output', $output);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Gagal menjalankan pembersihan: ' . $e->getMessage());
        }
    }
}
